import { FieldConfigMap } from "../../types/validate.type";
import { PermissionValidateType } from "./permissions.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<PermissionValidateType> = {
    id: { type: v.UUID, required: false },
    name: { type: v.<PERSON>EYCLOAK_PROHIBITED_CHARACTERS, required: true },
    description: { type: v.STRING, required: false },
};

export const receiveConfig: FieldConfigMap<PermissionValidateType> = {
    id: { type: v.UUID, required: true },
    name: { type: v.KEYCLOAK_PROHIBITED_CHARACTERS, required: true },
    description: { type: v.STRING, required: false },
};
