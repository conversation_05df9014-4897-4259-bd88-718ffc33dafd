import { validated } from "../utils/validations";
import { createConfig } from "../validations/roles/roles.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { ResponseMessageEnum as m } from "../enum/response";
import { FormatValidationError } from "../middlewares/errorHandler";
import { GroupRepresentation } from "../services/keycloak.service";
import { FieldConfigType } from "../types/validate.type";

export type RoleAttributes = {
    id: string;
    name: string;
};

export class RoleEntity {
    private readonly id: string;
    private roleData: RoleAttributes;

    constructor(id: string | null, roleData: RoleAttributes | null, customConfig?: Record<string, FieldConfigType>) {
        this.id = id!;
        this.roleData = this.initialize(roleData, customConfig);
    }

    private initialize(preData: RoleAttributes | null, customConfig?: Record<string, FieldConfigType>): RoleAttributes {
        this.validate(preData, customConfig);
        return {
            id: this.id,
            ...preData,
        } as RoleAttributes;
    }

    private validate(preData: RoleAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateRoleData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    private validateRoleData(data: RoleAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    public getId(): string {
        return this.id;
    }

    public getRoleResponse(): RoleAttributes {
        return {
            id: this.getId(),
            name: this.roleData.name,
        } as RoleAttributes;
    }

    public getRoleRepresentation(): GroupRepresentation {
        return {
            name: this.roleData.name,
        } as GroupRepresentation;
    }

    public setAttributes(roleData: RoleAttributes, customConfig?: Record<string, FieldConfigType>): void {
        this.roleData = this.initialize(roleData, customConfig);
    }
}
