import { FieldConfigMap } from "../../types/validate.type";
import { DiseaseCreateValidateType, DiseaseReceiveValidateType } from "./diseases.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<DiseaseCreateValidateType> = {
    name: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 2, maxLength: 80 },
};

export const receiveConfig: FieldConfigMap<DiseaseReceiveValidateType> = {
    diseaseId: { type: v.UUID, required: true },
    name: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 2, maxLength: 80 },
    createdAt: { type: v.DATE, required: true },
    updatedAt: { type: v.DATE, required: true },
    isDeleted: { type: v.BOOLEAN, required: true },
    deletedAt: { type: v.DATE, required: true, allowNull: true },
};
