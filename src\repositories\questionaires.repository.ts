import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { QuestionaireAttributes } from "../entities/questionaires.entity";
import { PetSpeciesEnum } from "../enum/pet";
import { withMongoDB } from "../db/mongodb";

export class QuestionaireRepository {
    async findOneById(id: string): Promise<QuestionaireAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<QuestionaireAttributes>("questionaires").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findOneBySpeciesAndType(species: PetSpeciesEnum, type: string): Promise<QuestionaireAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<QuestionaireAttributes>("questionaires").findOne(
                    {
                        species: species,
                        type: type,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findExistingBySpeciesAndType(species: PetSpeciesEnum, type: string): Promise<QuestionaireAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<QuestionaireAttributes>("questionaires").findOne(
                    {
                        species: species,
                        type: type,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<QuestionaireAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db
                    .collection<QuestionaireAttributes>("questionaires")
                    .find({ isDeleted: false })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: QuestionaireAttributes): Promise<QuestionaireAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const questionaireData: QuestionaireAttributes = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdQuestionaire = await db
                    .collection<QuestionaireAttributes>("questionaires")
                    .insertOne(questionaireData);

                if (!createdQuestionaire) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                return questionaireData;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<QuestionaireAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<QuestionaireAttributes>("questionaires").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<QuestionaireAttributes>("questionaires").updateOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new QuestionaireRepository();
