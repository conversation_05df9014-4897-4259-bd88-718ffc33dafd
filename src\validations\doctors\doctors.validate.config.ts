import { FieldConfigMap } from "../../types/validate.type";
import { DoctorCreateValidateType, DoctorReceiveValidateType } from "./doctors.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<DoctorCreateValidateType> = {
    prefix: { type: v.PREFIX, required: true },
    firstName: { type: v.LETTER_ONLY, required: true },
    lastName: { type: v.LETTER_ONLY, required: true },
    phoneNumber: { type: v.PHONE, required: true },
    email: { type: v.EMAIL, required: false, allowNull: true },
};

export const receiveConfig: FieldConfigMap<DoctorReceiveValidateType> = {
    id: { type: v.UUID, required: true },
    doctorInfo: {
        type: v.OBJECT,
        required: true,
        objectConfig: {
            prefix: { type: v.PREFIX, required: true },
            firstName: { type: v.LETTER_ONLY, required: true },
            lastName: { type: v.LETTER_ONLY, required: true },
            phoneNumber: { type: v.PHONE, required: true },
            email: { type: v.EMAIL, required: true, allowNull: true },
        },
    },
    createdAt: { type: v.DATE, required: true },
    updatedAt: { type: v.DATE, required: true },
    isDeleted: { type: v.BOOLEAN, required: true },
    deletedAt: { type: v.DATE, required: true, allowNull: true },
};
