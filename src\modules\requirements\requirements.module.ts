import { RequirementAttributes, RequirementEntity } from "../../entities/requirements.entity";
import {
    ModuleError,
    FormatValidationError,
    DefaultError,
    EntityError,
    RepositoryError,
    ServiceError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import { RequirementRequest, RequirementSearchListRequest } from "./dto/request";
import { RequirementResponse, RequirementListResponse } from "./dto/response";
import { RequirementRepository } from "../../repositories/requirements.repository";
import { PetSpeciesEnum } from "../../enum/pet";

export class RequirementModule {
    private readonly requirementRepository;
    constructor() {
        this.requirementRepository = new RequirementRepository();
    }
    public async getOne(id: string): Promise<RequirementResponse> {
        try {
            const requirementEntity = new RequirementEntity(id);
            const validId = requirementEntity.getId();
            const response = await this.requirementRepository.findRequirementById(validId);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_REQUIREMENT_FAILED);
        }
    }

    public async getList(filter: RequirementSearchListRequest): Promise<RequirementListResponse> {
        try {
            const response = await this.requirementRepository.findList(filter);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_REQUIREMENT_WITH_FILTER_FAILED);
        }
    }

    public async getAll(): Promise<RequirementResponse[]> {
        try {
            const response = await this.requirementRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_REQUIREMENT_FAILED);
        }
    }

    public async getAllBySpecies(species: PetSpeciesEnum): Promise<RequirementResponse[]> {
        try {
            const response = await this.requirementRepository.findAllRequirementsBySpecies(species);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_REQUIREMENT_SPECIES_FAILED);
        }
    }

    public async createMultiple(data: RequirementRequest[]): Promise<{ insertedCount: number }> {
        try {
            const validatedRequirements = data.map((recipeRequest) => {
                const requirementData = recipeRequest as RequirementAttributes;
                const requirementEntity = new RequirementEntity(null, requirementData);
                return requirementEntity.getAttributes();
            });

            const response = await this.requirementRepository.createRequirements(validatedRequirements);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_MULTIPLE_REQUIREMENTS_FAILED);
        }
    }

    public async create(data: RequirementRequest): Promise<RequirementResponse> {
        try {
            const requirementData = data as RequirementAttributes;
            const requirementEntity = new RequirementEntity(null, requirementData);
            const validData = requirementEntity.getAttributes();

            const existingRequirement = await this.requirementRepository.findExistingRequirementByNameAndSpecies(
                validData.name,
                validData.species
            );
            if (existingRequirement)
                throw new ModuleError(m.DUPLICATED_REQUIREMENT_NAME, d.DUPLICATED_REQUIREMENT_NAME);

            const response = await this.requirementRepository.create(validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_REQUIREMENT_FAILED);
        }
    }

    public async update(id: string, data: RequirementRequest): Promise<boolean> {
        try {
            const requirementData = data as RequirementAttributes;
            const requirementEntity = new RequirementEntity(id, requirementData);
            const validId = requirementEntity.getId();
            const validData = requirementEntity.getAttributes();

            const response = await this.requirementRepository.update(validId, validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_REQUIREMENT_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const validateId = new RequirementEntity(id);
            const validId = validateId.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.requirementRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_REQUIREMENT_FAILED);
        }
    }
}
