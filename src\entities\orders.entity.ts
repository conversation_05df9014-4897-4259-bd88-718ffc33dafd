import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig } from "../validations/orders/orders.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { NutrientTypeEnum } from "../enum/requirement";

export type NutrientDetails = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    value: number;
    min: number;
    max: number;
    isInRange: boolean;
};

export type AdditiveNutrientsDetails = {
    rawMatName: string;
    rawMatAmount: number;
    rawMatNutrients: {
        nutrientName: string;
        unit: string;
        type: string;
        value: number;
    }[];
};

export type RecipeDetails = {
    me: number;
    dailyAmount: number;
    mealsPerDay: number;
    amountPerMeal: number;
    batchMakingFor: number;
};

export type MatchingRecipes = {
    recipeName: string;
    matchPercentage: number;
    recipeNutrients: NutrientDetails[];
    additives: AdditiveNutrientsDetails[] | null;
    recipeDetails: RecipeDetails;
};

export type OrderAttributes = {
    id: string;
    visitId: string;
    petId: string;
    ownerId: string;
    orderInfo: MatchingRecipes[];
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export class OrderEntity {
    private readonly id: string | null;
    private readonly orderData: OrderAttributes;
    constructor(
        id: string | null = null,
        preData: OrderAttributes | null = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.orderData = this.initialize(preData, customConfig);
    }

    private initialize(
        preData: OrderAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): OrderAttributes {
        try {
            this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                visitId: preData?.visitId,
                petId: preData?.petId,
                ownerId: preData?.ownerId,
                orderInfo: preData?.orderInfo,
            } as OrderAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: OrderAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateOrderData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateOrderData(data: OrderAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.orderData.id;
    }

    public getAttributes(): OrderAttributes {
        return this.orderData;
    }
}
