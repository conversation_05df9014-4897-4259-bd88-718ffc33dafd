import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { RequirementModule } from "../modules/requirements/requirements.module";
import { RequirementRequest, RequirementSearchListRequest } from "../modules/requirements/dto/request";
import { PetSpeciesEnum } from "../enum/pet";
import { ListLimitConstants } from "../constants/defaultValue";

export class RequirementController {
    private readonly requirementModule: RequirementModule;
    public router: Router;
    constructor(requirementModule: RequirementModule) {
        this.requirementModule = requirementModule;
        this.router = express.Router();
        this.routes();
    }

    private routes() {
        this.router.get("/species", this.getAllBySpecies.bind(this));
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/bulk", this.createMultiple.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }

    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.requirementModule.getOne(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.requirementModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const filter: RequirementSearchListRequest = {
                species: req.query.species as PetSpeciesEnum,
                name: req.query.name as string,
                limit: Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT,
                page: Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE,
            };
            const response = await this.requirementModule.getList(filter);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAllBySpecies(req: Request, res: Response, next: NextFunction) {
        try {
            const species = req.query.species as PetSpeciesEnum;
            const response = await this.requirementModule.getAllBySpecies(species);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async createMultiple(req: Request, res: Response, next: NextFunction) {
        try {
            const body: RequirementRequest[] = req.body;
            const response = await this.requirementModule.createMultiple(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as RequirementRequest;
            const response = await this.requirementModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body;
            const response = await this.requirementModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.requirementModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
