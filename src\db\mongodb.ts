import { MongoClient, Db } from "mongodb";

const MONGO_URI: string = String(process.env.MONGO_URI);
const DB_NAME: string = String(process.env.DB_NAME);

let dbInstance: Db | null = null;

// Function to initialize and sync database
export const initMongoDB = async (): Promise<void> => {
    try {
        const client = new MongoClient(MONGO_URI);
        await client.connect();
        dbInstance = client.db(DB_NAME);
        console.log("MongoDB Connection Initialized");
    } catch (error) {
        console.error("Unable to connect to the database:", error);
    }
};

export const withMongoDB = async <T>(queryFunction: (db: Db) => Promise<T>): Promise<T> => {
    let client: MongoClient | null = null;
    try {
        client = new MongoClient(MONGO_URI);
        await client.connect();
        const db = client.db(DB_NAME);
        return await queryFunction(db);
    } catch (error) {
        console.error("MongoDB Query Error:", error);
        throw error;
    } finally {
        if (client) {
            await client.close();
        }
    }
};
