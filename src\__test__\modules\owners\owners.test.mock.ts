import { v7 as uuidv7 } from "uuid";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../../enum/response";

const validOwnerUUID1 = uuidv7();
const validOwnerUUID2 = uuidv7();

const validOwnerData1 = {
    id: validOwnerUUID1,
    ownerInfo: {
        firstName: "<PERSON>",
        lastName: "Doe",
        phoneNumber: "0991234567",
        email: "<EMAIL>",
    },
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
};

const validOwnerData2 = {
    id: validOwnerUUID2,
    ownerInfo: {
        firstName: "<PERSON>",
        lastName: "Manoo",
        phoneNumber: "0991234568",
        email: "<EMAIL>",
    },
    isDeleted: false,
    createdAt: new Date(),
    updatedAt: new Date(),
    deletedAt: null,
};
export const getAllMocks = {
    request: {
        validParams: {
            noFilter: {
                filter: {},
            },
            firstNameFilter: {
                filter: {
                    text: "John",
                },
            },
            lastNameFilter: {
                filter: {
                    text: "Doe",
                },
            },
            fullNameFilter: {
                filter: {
                    text: "John Doe",
                },
            },
            emailFilter: {
                filter: {
                    text: "<EMAIL>",
                },
            },
            phoneNumberFilter: {
                filter: {
                    text: "0991234567",
                },
            },
        },
    },
    response: {
        noFilter: [validOwnerData1, validOwnerData2],
        firstNameFilter: [validOwnerData1],
        lastNameFilter: [validOwnerData1],
        fullNameFilter: [validOwnerData1],
        emailFilter: [validOwnerData1],
        phoneNumberFilter: [validOwnerData1],
        empty: [],
    },
    error: {
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_OWNER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};
