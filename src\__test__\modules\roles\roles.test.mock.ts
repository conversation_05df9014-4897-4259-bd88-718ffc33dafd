import { v4 as uuidv4 } from "uuid";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";
import { RoleAttributes } from "src/entities/roles.entity";

const roleAttributesOne = {
    id: uuidv4(),
    name: "admin",
};
const roleAttributesTwo = {
    id: uuidv4(),
    name: "user",
};
export const getAllMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
    },
    response: {
        success: [
            {
                ...roleAttributesOne,
            },
            {
                ...roleAttributesTwo,
            },
        ],
        successWithoutDto: [
            {
                ...roleAttributesOne,
                somethingElse: "value",
            },
            {
                ...roleAttributesTwo,
                somethingElse: "value",
            },
        ],
        empty: [],
        invalidKey: [
            {
                somethingElse: "value",
            },
        ],
        invalidValue: [
            {
                id: "1",
                name: "2",
            },
        ],
    },
    error: {
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.LETTER_ONLY,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_ROLES_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};

export const getOneMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: roleAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
    },
    response: {
        success: {
            ...roleAttributesOne,
        },
        successWithoutDto: {
            ...roleAttributesOne,
            somethingElse: "value",
        },
        invalidKey: {
            somethingElse: "value",
        },
        invalidValue: {
            id: "1",
            name: "2",
        },
    },
    error: {
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.LETTER_ONLY,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ONE_ROLE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};

export const getListMocks = {
    request: {
        validToken: "valid-token",
        validLimit: 10,
        validLimitOne: 1,
        validPage: 1,
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
    },
    response: {
        success: getAllMocks.response.success,
        successWithoutDto: getAllMocks.response.successWithoutDto,
        successWithPagination: [
            {
                ...roleAttributesOne,
            },
        ],
        empty: [],
        invalidKey: [
            {
                somethingElse: "value",
            },
        ],
        invalidValue: [
            {
                id: "1",
                name: "2",
            },
        ],
    },
    error: {
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.LETTER_ONLY,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_ROLES_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};

export const createMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validBody: {
            name: "admin",
        },
        invalidBodyValue: {
            name: "1",
        },
        missingBodyKey: {
            somethingElse: "value",
        } as any as RoleAttributes,
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.LETTER_ONLY,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_ROLE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};

export const updateMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: roleAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
        validBody: {
            name: "admin",
        },
        invalidBody: {
            name: "1",
        },
    },
    response: {
        success: {
            data: null,
        },
    },
    error: {
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidNameError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.LETTER_ONLY,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.LETTER_ONLY,
            },
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.UPDATE_ROLE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};

export const deleteMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: roleAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
    },
    response: {
        success: {
            data: null,
        },
    },
    error: {
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.DELETE_ROLE_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};
