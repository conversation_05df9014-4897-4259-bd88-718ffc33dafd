import { TestCase } from "../../utils/testRunner";
import { PermissionModule } from "../../../modules/permissions/permissions.module";
import { getAllMocks, getListMocks, getOneMocks, createMocks, updateMocks, deleteMocks } from "./permissions.test.mock";
import { PermissionsResponse, PermissionsListResponse } from "../../../modules/permissions/dto/response";
import { PermissionsRequest } from "../../../modules/permissions/dto/request";
import { DefaultError, ServiceError } from "../../../middlewares/errorHandler";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../../../enum/response";
import { mockService as roleMockService } from "../roles/roles.test.config";

type MethodTypeMapper = {
    getAll: { input: { token: string }; output: PermissionsResponse[] };
    getOne: { input: { token: string; name: string }; output: PermissionsResponse };
    getList: {
        input: { token: string; limit: number; page: number };
        output: PermissionsListResponse;
    };
    create: { input: { token: string; data: PermissionsRequest }; output: void };
    update: { input: { token: string; name: string; data: PermissionsRequest }; output: void };
    delete: { input: { token: string; name: string }; output: void };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof PermissionModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

// Mock repository object
export const mockService = {
    ...roleMockService,
    getClientUuname: jest.fn(),
    getClientRoles: jest.fn(),
    getClientRoleByName: jest.fn(),
    createClientRole: jest.fn(),
    updateClientRole: jest.fn(),
    deleteClientRole: jest.fn(),
};
// Replace the real Repository or Service class with our mock version
jest.mock("../../../services/keycloak.service", () => ({
    KeycloakService: jest.fn(() => mockService),
}));

const moduleInstance = new PermissionModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Permissions",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all permissions",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getAllMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty array when no permissions found",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getAllMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: getAllMocks.request.invalidToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getAllMocks.request.forbiddenToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getAllMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.validationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getAllMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getOne: {
            cases: [
                {
                    name: "[SUCCESS] should return one permission",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            mockService.getClientRoleByName.mockResolvedValue(getOneMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: getOneMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getClientRoleByName.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: getOneMocks.request.invalidToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getClientRoleByName.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getOneMocks.request.forbiddenToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getClientRoleByName.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid name occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.invalidParams.name,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.validationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when permission not found",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.getClientRoleByName.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            mockService.getClientRoleByName.mockResolvedValue(getOneMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            mockService.getClientRoleByName.mockResolvedValue(getOneMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.validationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            name: getOneMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getClientRoleByName.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleByName,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return all permissions",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getListMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.success,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of permissions in case of pagination",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimitOne,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getListMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.successWithPagination,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no permissions exist",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.empty,
                            count: getListMocks.response.empty.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: getListMocks.request.invalidToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getListMocks.request.forbiddenToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getClientRoles.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getListMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getClientRoles.mockResolvedValue(getListMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoles,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should create permission",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.createClientRole.mockResolvedValue(createMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.createClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: createMocks.request.invalidToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.createClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: createMocks.request.forbiddenToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.createClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error for unexpected error",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.createClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.invalidBodyValue,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.missingBodyKey,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createClientRole,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        update: {
            cases: [
                {
                    name: "[SUCCESS] should update permission",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.validParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.updateClientRole.mockResolvedValue(updateMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.validParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.validParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.updateClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error for unexpected error",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.validParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.updateClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: updateMocks.request.invalidToken,
                            name: updateMocks.request.validParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.updateClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: updateMocks.request.forbiddenToken,
                            name: updateMocks.request.validParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.updateClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when permission not found",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.notFoundParams.name,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.updateClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator UUID occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.invalidParams.name,
                            data: updateMocks.request.validBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator name occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            name: updateMocks.request.invalidParams.name,
                            data: updateMocks.request.invalidBodyValue,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.name, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        delete: {
            cases: [
                {
                    name: "[SUCCESS] should delete a permission",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            mockService.deleteClientRole.mockResolvedValue(deleteMocks.response.success.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.deleteClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: deleteMocks.request.invalidToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.deleteClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: deleteMocks.request.forbiddenToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.deleteClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid name occurs",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            name: deleteMocks.request.invalidParams.name,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when permission not found",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.deleteClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            name: deleteMocks.request.validParams.name,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.deleteClientRole.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.name);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteClientRole,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
