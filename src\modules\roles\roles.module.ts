import { ServiceError, DefaultError, FormatValidationError } from "../../middlewares/errorHandler";
import { KeycloakService } from "../../services/keycloak.service";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { RolesResponse, RolesListResponse } from "./dto/response";
import { RolesRequest } from "./dto/request";
import { RoleEntity, RoleAttributes } from "../../entities/roles.entity";
import { receiveConfig } from "../../validations/roles/roles.validate.config";

export class RoleModule {
    private authService!: KeycloakService;
    private initializedAuthService(token: string): void {
        try {
            this.authService = new KeycloakService(token);
        } catch (error) {
            console.error(e.SERVICE_ERROR, error);
            throw new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_CONFIGURED_ERROR);
        }
    }
    async getAll(token: string): Promise<RolesResponse[]> {
        try {
            let rolesResponse: RolesResponse[] = [];
            this.initializedAuthService(token);
            const roles = await this.authService.getGroups();
            rolesResponse = roles.map((role) => {
                const roleEntity = new RoleEntity(role.id, role, receiveConfig);
                const roleResponse = roleEntity.getRoleResponse();
                return roleResponse;
            });
            return rolesResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_ROLES_FAILED);
        }
    }
    async getOne(token: string, id: string): Promise<RolesResponse> {
        try {
            this.initializedAuthService(token);
            const roleEntity = new RoleEntity(id, null);
            const roleId = roleEntity.getId();
            const role = await this.authService.getGroupById(roleId);
            roleEntity.setAttributes(role, receiveConfig);
            const roleResponse = roleEntity.getRoleResponse();
            return roleResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_ROLE_FAILED);
        }
    }
    async getList(token: string, limit: number, page: number): Promise<RolesListResponse> {
        try {
            this.initializedAuthService(token);
            const roles = await this.authService.getGroups();
            const rolesResponse = roles.map((role) => {
                const roleEntity = new RoleEntity(role.id, role, receiveConfig);
                const roleResponse = roleEntity.getRoleResponse();
                return roleResponse;
            });
            // pagination
            let offset: number = 0;
            let rolesResponseWithPagination = rolesResponse;
            if (limit && page) {
                offset = limit * (page - 1);
                rolesResponseWithPagination = rolesResponse.slice(offset, limit * page);
            }
            return {
                rows: rolesResponseWithPagination,
                count: rolesResponse.length,
            };
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_ROLES_FAILED);
        }
    }
    async create(token: string, data: RolesRequest): Promise<void> {
        try {
            const roleEntity = new RoleEntity(null, data as RoleAttributes);
            this.initializedAuthService(token);
            const roleData = roleEntity.getRoleRepresentation();
            await this.authService.createGroup(roleData);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_ROLE_FAILED);
        }
    }
    async update(token: string, id: string, data: RolesRequest): Promise<void> {
        try {
            const dataAttributes = { id, ...data };
            const roleEntity = new RoleEntity(id, dataAttributes, receiveConfig);
            this.initializedAuthService(token);
            const roleId = roleEntity.getId();
            const roleData = roleEntity.getRoleRepresentation();
            await this.authService.updateGroup(roleId, roleData);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_ROLE_FAILED);
        }
    }
    async delete(token: string, id: string): Promise<void> {
        try {
            const roleEntity = new RoleEntity(id, null);
            this.initializedAuthService(token);
            const roleId = roleEntity.getId();
            await this.authService.deleteGroup(roleId);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_ROLE_FAILED);
        }
    }
}
