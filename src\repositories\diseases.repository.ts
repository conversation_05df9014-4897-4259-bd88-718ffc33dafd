import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { DiseaseAttributes } from "../entities/diseases.entity";
import { withMongoDB } from "../db/mongodb";
import { Db } from "mongodb";
import { DATABASE_TABLE } from "../constants/database";

type Filter = {
    isDeleted: boolean;
    name?: string;
};
const DefaultFilter: Filter = {
    isDeleted: false,
};
export class DiseaseRepository {
    async initCollection(db: Db) {
        return db.collection<DiseaseAttributes>(DATABASE_TABLE.DISEASES);
    }
    async getAll(): Promise<DiseaseAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const collection = await this.initCollection(db);
                return collection.find(DefaultFilter).toArray();
            });
        } catch (error) {
            console.error(error);
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async create(data: DiseaseAttributes): Promise<DiseaseAttributes> {
        try {
            const diseaseData: DiseaseAttributes = {
                ...data,
                isDeleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                deletedAt: null,
            };
            const createdDisease = await withMongoDB(async (db) => {
                const collection = await this.initCollection(db);
                return collection.insertOne(diseaseData);
            });
            if (!createdDisease) {
                throw new QueryError(m.INSERTION_FAILED);
            }

            return diseaseData;
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async findByName(name: string): Promise<DiseaseAttributes | null> {
        try {
            const filter: Filter = { ...DefaultFilter };
            if (name) filter.name = name;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);
                const disease = await collection.findOne(filter);
                return disease;
            });
        } catch (error) {
            console.error(error);
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async update(id: string, data: Partial<DiseaseAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<DiseaseAttributes>(DATABASE_TABLE.DISEASES).updateOne(
                    { diseaseId: id, ...DefaultFilter },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw error;
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<DiseaseAttributes>(DATABASE_TABLE.DISEASES).updateOne(
                    { diseaseId: id, ...DefaultFilter },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw error;
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}
