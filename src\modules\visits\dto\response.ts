import { PetHealthSubmitType, PetRequirementSubmitType, PetNutritionSubmitType, PetProductMatchSubmitType } from "../../../entities/visits.entity";
import { PetInfo } from "../../../entities/pets.entity";
import { VisitStatusEnum, VisitStepEnum } from "../../../enum/visit";
import { NutrientTypeEnum } from "../../../enum/requirement";
import { DoctorInfo } from "../../../entities/doctors.entity";
import { PetGenderTypeEnum, PetSpeciesEnum } from "../../../enum/pet";

export type VisitResponse = {
    id?: string;
    petId: string;
    doctorId: string;
    petInfo: PetInfo;
    doctorInfo: DoctorInfo;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    petHealth: PetHealthSubmitType | null;
    petRequirement: PetRequirementSubmitType | null;
    petNutrition: PetNutritionSubmitType | null;
    petProductMatch: PetProductMatchSubmitType | null;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type VisitWithOwnerDetailResponse = {
    petInfo: {
        hn: string;
        name: string;
        gender: PetGenderTypeEnum;
        species: PetSpeciesEnum;
        breed: string;
    };
    doctorInfo: {
        prefix: string;
        firstName: string;
        lastName: string;
    };
    ownerInfo: {
        firstName: string;
        lastName: string;
        phoneNumber: string;
        email: string;
    };
    id: string;
    petId: string;
    doctorId: string;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    createdAt: Date;
}

export type VisitWithOwnerListResponse = {
    rows: VisitWithOwnerDetailResponse[];
    count: number;
}

export type NutrientData = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
};

export type MapNutrientsResponse = {
    nutrients: NutrientData[];
};

export type VisitStatusCountsResponse = {
    waiting: number;
    inProgress: number;
    completed: number;
};