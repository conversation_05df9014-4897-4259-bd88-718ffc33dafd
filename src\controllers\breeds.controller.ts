import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { BreedModule } from "../modules/breeds/breeds.module";
import { BreedRequest, BreedSearchRequest } from "../modules/breeds/dto/request";
import { PetSpeciesEnum } from "../enum/pet";
import { ListLimitConstants } from "../constants/defaultValue";

export class BreedController {
    private readonly breedModule: BreedModule;
    public router: Router;
    constructor(breedModule: BreedModule) {
        this.breedModule = breedModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/filter", this.getAllWithFilter.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/bulk", this.createMultiple.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }
    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.breedModule.getOne(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.breedModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAllWithFilter(req: Request, res: Response, next: NextFunction) {
        try {
            const filter: BreedSearchRequest = {
                species: req.query.species as PetSpeciesEnum,
                breedName: req.query.breedName as string,
            };
            const response = await this.breedModule.getAllWithFilter(filter);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const limit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            const page = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;

            const response = await this.breedModule.getList(limit, page);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async createMultiple(req: Request, res: Response, next: NextFunction) {
        try {
            const body: BreedRequest[] = req.body;
            const response = await this.breedModule.createMultiple(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body;
            const response = await this.breedModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body;
            const response = await this.breedModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.breedModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
