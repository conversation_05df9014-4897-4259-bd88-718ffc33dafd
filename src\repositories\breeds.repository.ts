import { QueryError, RepositoryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { BreedAttributes } from "../entities/breeds.entity";
import { withMongoDB } from "../db/mongodb";
import { BreedSearchRequest } from "../modules/breeds/dto/request";
import { PetSpeciesEnum } from "../enum/pet";

interface BreedQuery {
    isDeleted: boolean;
    species?: PetSpeciesEnum;
    breedName?: { $regex: string; $options: string };
}

export class BreedRepository {
    async findBreedById(id: string): Promise<BreedAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<BreedAttributes>("breeds").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<BreedAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<BreedAttributes>("breeds").find({ isDeleted: false }).toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAllWithFilter(filter: BreedSearchRequest): Promise<BreedAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const { species, breedName } = filter;
                const query: BreedQuery = { isDeleted: false };

                if (species) query.species = species;

                // breedName%
                if (breedName) query.breedName = { $regex: `^${breedName}`, $options: "i" };

                const response = await db
                    .collection<BreedAttributes>("breeds")
                    .find(query, {
                        projection: {
                            _id: 0,
                            breedName: 1,
                        },
                    })
                    .sort({ breedName: 1 })
                    .toArray();

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(limit: number, page: number): Promise<{ rows: BreedAttributes[]; count: number }> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const offset = (page - 1) * limit;

                const count = await db.collection<BreedAttributes>("breeds").countDocuments(filter);

                const rows = await db
                    .collection<BreedAttributes>("breeds")
                    .find(filter, {
                        projection: {
                            _id: 0,
                            id: 1,
                            species: 1,
                            breedName: 1,
                        },
                    })
                    .sort({ breedName: 1 })
                    .skip(offset)
                    .limit(limit)
                    .toArray();

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async createBreeds(datas: BreedAttributes[]): Promise<{ insertedCount: number }> {
        try {
            return await withMongoDB(async (db) => {
                const breedsData = datas.map((data) => ({
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                }));

                const result = await db.collection<BreedAttributes>("breeds").insertMany(breedsData);

                if (!result.insertedCount) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                return { insertedCount: result.insertedCount };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: BreedAttributes): Promise<BreedAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const appointmentData = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdAppointment = await db.collection<BreedAttributes>("breeds").insertOne(appointmentData);

                if (!createdAppointment) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await db
                    .collection<BreedAttributes>("breeds")
                    .findOne({ id: appointmentData.id }, { projection: { _id: 0 } });

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<BreedAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<BreedAttributes>("breeds").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<BreedAttributes>("breeds").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new BreedRepository();
