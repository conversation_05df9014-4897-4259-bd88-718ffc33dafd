import { FieldConfigMap } from "../../types/validate.type";
import { OrderValidateType } from "./orders.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<OrderValidateType> = {
    visitId: { type: v.UUID, required: true },
    petId: { type: v.UUID, required: true },
    ownerId: { type: v.UUID, required: true },
    orderInfo: { type: v.RECIPE_DETAILS, required: true },
};
