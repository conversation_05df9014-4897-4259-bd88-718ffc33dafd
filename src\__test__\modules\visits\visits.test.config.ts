import {
    VisitRequest,
    CreateVisitFromPetRequest,
    PetInfoSubmitRequest,
    PetHealthSubmitRequest,
    PetRequirementSubmitRequest,
    PetNutritionSubmitRequest,
    PetProductMatchSubmitRequest,
    OrderSummarySubmitRequest,
    GetVisitListFilterRequest,
    GetVisitStatusCountsFilterRequest,
} from "../../../modules/visits/dto/request";
import {
    VisitResponse,
    MapNutrientsResponse,
    VisitWithOwnerListResponse,
    VisitStatusCountsResponse,
} from "../../../modules/visits/dto/response";
import { TestCase } from "../../utils/testRunner";
import { VisitModule } from "../../../modules/visits/visits.module";
import { createFromPetMocks, getListMocks, getStatusCountsMocks } from "./visits.test.mock";
import { DefaultError } from "../../../middlewares/errorHandler";

type MethodTypeMapper = {
    create: { input: { data: VisitRequest }; output: VisitResponse };
    createFromPet: { input: { data: CreateVisitFromPetRequest }; output: void };
    handlePetInfoStep: { input: PetInfoSubmitRequest; output: boolean };
    handlePetHealthStep: { input: PetHealthSubmitRequest; output: boolean };
    handlePetRequirementStep: { input: PetRequirementSubmitRequest; output: boolean };
    handlePetNutritionStep: { input: PetNutritionSubmitRequest; output: boolean };
    handlePetProductMatchStep: { input: PetProductMatchSubmitRequest; output: boolean };
    handleOrderSummaryStep: { input: OrderSummarySubmitRequest; output: boolean };
    getPersonalizedNutrients: { input: string; output: MapNutrientsResponse };
    getOne: { input: string; output: VisitResponse | null };
    getAll: { input: void; output: VisitResponse[] };
    getList: {
        input: { limit: number; page: number; filter?: GetVisitListFilterRequest };
        output: VisitWithOwnerListResponse;
    };
    getStatusCounts: { input: { filter: GetVisitStatusCountsFilterRequest }; output: VisitStatusCountsResponse };
    update: { input: { id: string; data: VisitRequest }; output: boolean };
    delete: { input: string; output: boolean };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof VisitModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

const mockVisitRepository = {
    create: jest.fn(),
    findList: jest.fn(),
    findStatusCounts: jest.fn(),
};

jest.mock("../../../repositories/visits.repository", () => ({
    VisitRepository: jest.fn(() => mockVisitRepository),
}));

// Mock pet module
const mockPetModule = {
    createWithOwner: jest.fn(),
};

jest.mock("../../../modules/pets/pets.module", () => ({
    PetModule: jest.fn(() => mockPetModule),
}));

const moduleInstance = new VisitModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Visits",
    testCases: {
        createFromPet: {
            cases: [
                {
                    name: "[SUCCESS] should create a new visit with new pet and new owner",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithNewOwner,
                        },
                        mocks: () => {
                            mockVisitRepository.create.mockResolvedValue(createFromPetMocks.response.success);
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: createFromPetMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should create a new visit with new pet and existing owner",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithExistOwner,
                        },
                        mocks: () => {
                            mockVisitRepository.create.mockResolvedValue(createFromPetMocks.response.success);
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: createFromPetMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when owner info is invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidOwnerInfo as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.invalidOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when pet info is invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidPetInfo as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.invalidPetInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidPetInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when doctor info is invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidDoctorInfo as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidDoctorInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when visit attributes are invalid",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .invalidVisitAttributes as unknown as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.invalidVisitAttributes,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when pet hn already exist",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.existingPetHN as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.petHNAlreadyExist
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.petHNAlreadyExist,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when owner phone number already exist",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .existingOwnerPhoneNumber as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.ownerPhoneNumberAlreadyExist
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.ownerPhoneNumberAlreadyExist,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when not define owner id and owner info",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .notDefineOwnerIdandOwnerInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.notDefineOwnerIdandOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.notDefineOwnerIdandOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when define both owner id and owner info",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody
                                .defineBothOwnerIdandOwnerInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.defineBothOwnerIdandOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.defineBothOwnerIdandOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when owner info is empty",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.emptyOwnerInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.emptyOwnerInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.emptyOwnerInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when pet info is empty",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.invalidBody.emptyPetInfo as CreateVisitFromPetRequest,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.emptyPetInfo
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.emptyPetInfo,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw unexpected error from pet module",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithNewOwner,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockRejectedValue(
                                createFromPetMocks.petModuleResponse.error.unexpectedError
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.unexpectedErrorFromPetModule,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: false,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw unexpected error from visit module",
                    setup: {
                        input: {
                            data: createFromPetMocks.request.validBody.validWithNewOwner,
                        },
                        mocks: () => {
                            mockPetModule.createWithOwner.mockResolvedValue(
                                createFromPetMocks.petModuleResponse.success
                            );
                            mockVisitRepository.create.mockRejectedValue(
                                createFromPetMocks.error.unexpectedErrorFromVisitModule
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createFromPet(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createFromPetMocks.error.unexpectedErrorFromVisitModule,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.create,
                                called: true,
                            },
                            {
                                method: mockPetModule.createWithOwner,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return list of visits with default filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.defaultFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.defaultFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with default filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with pet hn id filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petHnIdFilter.limit,
                            page: getListMocks.request.validParams.petHnIdFilter.page,
                            filter: getListMocks.request.validParams.petHnIdFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.petHnIdFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.petHnIdFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petHnIdFilter.limit,
                                    getListMocks.request.validParams.petHnIdFilter.page,
                                    getListMocks.request.validParams.petHnIdFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with pet hn id filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petHnIdFilter.limit,
                            page: getListMocks.request.validParams.petHnIdFilter.page,
                            filter: getListMocks.request.validParams.petHnIdFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petHnIdFilter.limit,
                                    getListMocks.request.validParams.petHnIdFilter.page,
                                    getListMocks.request.validParams.petHnIdFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with pet name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petNameFilter.limit,
                            page: getListMocks.request.validParams.petNameFilter.page,
                            filter: getListMocks.request.validParams.petNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.petNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.petNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petNameFilter.limit,
                                    getListMocks.request.validParams.petNameFilter.page,
                                    getListMocks.request.validParams.petNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with pet name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petNameFilter.limit,
                            page: getListMocks.request.validParams.petNameFilter.page,
                            filter: getListMocks.request.validParams.petNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petNameFilter.limit,
                                    getListMocks.request.validParams.petNameFilter.page,
                                    getListMocks.request.validParams.petNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with owner firstname filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFirstNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFirstNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFirstNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.ownerFirstNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerFirstNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFirstNameFilter.limit,
                                    getListMocks.request.validParams.ownerFirstNameFilter.page,
                                    getListMocks.request.validParams.ownerFirstNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with owner firstname filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFirstNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFirstNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFirstNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFirstNameFilter.limit,
                                    getListMocks.request.validParams.ownerFirstNameFilter.page,
                                    getListMocks.request.validParams.ownerFirstNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with owner lastname filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerLastNameFilter.limit,
                            page: getListMocks.request.validParams.ownerLastNameFilter.page,
                            filter: getListMocks.request.validParams.ownerLastNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.ownerLastNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerLastNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerLastNameFilter.limit,
                                    getListMocks.request.validParams.ownerLastNameFilter.page,
                                    getListMocks.request.validParams.ownerLastNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with owner lastname filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerLastNameFilter.limit,
                            page: getListMocks.request.validParams.ownerLastNameFilter.page,
                            filter: getListMocks.request.validParams.ownerLastNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerLastNameFilter.limit,
                                    getListMocks.request.validParams.ownerLastNameFilter.page,
                                    getListMocks.request.validParams.ownerLastNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with owner fullname filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFullNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFullNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFullNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.ownerFullNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerFullNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFullNameFilter.limit,
                                    getListMocks.request.validParams.ownerFullNameFilter.page,
                                    getListMocks.request.validParams.ownerFullNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with owner fullname filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFullNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFullNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFullNameFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFullNameFilter.limit,
                                    getListMocks.request.validParams.ownerFullNameFilter.page,
                                    getListMocks.request.validParams.ownerFullNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with doctor id filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.doctorIdFilter.limit,
                            page: getListMocks.request.validParams.doctorIdFilter.page,
                            filter: getListMocks.request.validParams.doctorIdFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.doctorIdFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.doctorIdFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.doctorIdFilter.limit,
                                    getListMocks.request.validParams.doctorIdFilter.page,
                                    getListMocks.request.validParams.doctorIdFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with doctor id filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.doctorIdFilter.limit,
                            page: getListMocks.request.validParams.doctorIdFilter.page,
                            filter: getListMocks.request.validParams.doctorIdFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.doctorIdFilter.limit,
                                    getListMocks.request.validParams.doctorIdFilter.page,
                                    getListMocks.request.validParams.doctorIdFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with date from filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.dateFromFilter.limit,
                            page: getListMocks.request.validParams.dateFromFilter.page,
                            filter: getListMocks.request.validParams.dateFromFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.dateFromFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.dateFromFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.dateFromFilter.limit,
                                    getListMocks.request.validParams.dateFromFilter.page,
                                    getListMocks.request.validParams.dateFromFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with date from filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.dateFromFilter.limit,
                            page: getListMocks.request.validParams.dateFromFilter.page,
                            filter: getListMocks.request.validParams.dateFromFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.dateFromFilter.limit,
                                    getListMocks.request.validParams.dateFromFilter.page,
                                    getListMocks.request.validParams.dateFromFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with date to filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.dateToFilter.limit,
                            page: getListMocks.request.validParams.dateToFilter.page,
                            filter: getListMocks.request.validParams.dateToFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.dateToFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.dateToFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.dateToFilter.limit,
                                    getListMocks.request.validParams.dateToFilter.page,
                                    getListMocks.request.validParams.dateToFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with date to filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.dateToFilter.limit,
                            page: getListMocks.request.validParams.dateToFilter.page,
                            filter: getListMocks.request.validParams.dateToFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.dateToFilter.limit,
                                    getListMocks.request.validParams.dateToFilter.page,
                                    getListMocks.request.validParams.dateToFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of visits with date range filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.dateRangeFilter.limit,
                            page: getListMocks.request.validParams.dateRangeFilter.page,
                            filter: getListMocks.request.validParams.dateRangeFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.dateRangeFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.dateRangeFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.dateRangeFilter.limit,
                                    getListMocks.request.validParams.dateRangeFilter.page,
                                    getListMocks.request.validParams.dateRangeFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with date range filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.dateRangeFilter.limit,
                            page: getListMocks.request.validParams.dateRangeFilter.page,
                            filter: getListMocks.request.validParams.dateRangeFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.dateRangeFilter.limit,
                                    getListMocks.request.validParams.dateRangeFilter.page,
                                    getListMocks.request.validParams.dateRangeFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw error with invalid filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.invalidParams.invalidFilter.limit,
                            page: getListMocks.request.invalidParams.invalidFilter.page,
                            filter: getListMocks.request.invalidParams.invalidFilter.filter,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidFilter,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw unexpected error from visit module",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockVisitRepository.findList.mockRejectedValue(getListMocks.error.unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getStatusCounts: {
            cases: [
                {
                    name: "[SUCCESS] should return status counts of visits with date range filter",
                    setup: {
                        input: {
                            filter: getStatusCountsMocks.request.validParams.defaultFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findStatusCounts.mockResolvedValue(
                                getStatusCountsMocks.response.defaultFilter
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getStatusCounts(input.filter);
                    },
                    expectation: {
                        result: getStatusCountsMocks.response.defaultFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findStatusCounts,
                                called: true,
                                withArgs: [
                                    getStatusCountsMocks.request.validParams.defaultFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return status counts of visits with only dateFrom filter",
                    setup: {
                        input: {
                            filter: getStatusCountsMocks.request.validParams.dateFromOnlyFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findStatusCounts.mockResolvedValue(
                                getStatusCountsMocks.response.dateFromOnlyFilter
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getStatusCounts(input.filter);
                    },
                    expectation: {
                        result: getStatusCountsMocks.response.dateFromOnlyFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findStatusCounts,
                                called: true,
                                withArgs: [
                                    getStatusCountsMocks.request.validParams.dateFromOnlyFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return status counts of visits with only dateTo filter",
                    setup: {
                        input: {
                            filter: getStatusCountsMocks.request.validParams.dateToOnlyFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findStatusCounts.mockResolvedValue(
                                getStatusCountsMocks.response.dateToOnlyFilter
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getStatusCounts(input.filter);
                    },
                    expectation: {
                        result: getStatusCountsMocks.response.dateToOnlyFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findStatusCounts,
                                called: true,
                                withArgs: [
                                    getStatusCountsMocks.request.validParams.dateToOnlyFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return status counts of visits with no filter",
                    setup: {
                        input: {
                            filter: getStatusCountsMocks.request.validParams.noFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findStatusCounts.mockResolvedValue(
                                getStatusCountsMocks.response.noFilter
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getStatusCounts(input.filter);
                    },
                    expectation: {
                        result: getStatusCountsMocks.response.noFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockVisitRepository.findStatusCounts,
                                called: true,
                                withArgs: [
                                    getStatusCountsMocks.request.validParams.noFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error with invalid filter",
                    setup: {
                        input: {
                            filter: getStatusCountsMocks.request.invalidParams.invalidFilter.filter,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getStatusCounts(input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getStatusCountsMocks.error.invalidFilter,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.findStatusCounts,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw unexpected error from visit module",
                    setup: {
                        input: {
                            filter: getStatusCountsMocks.request.validParams.defaultFilter.filter,
                        },
                        mocks: () => {
                            mockVisitRepository.findStatusCounts.mockRejectedValue(
                                getStatusCountsMocks.error.unexpectedError
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getStatusCounts(input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getStatusCountsMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockVisitRepository.findStatusCounts,
                                called: true,
                                withArgs: [
                                    getStatusCountsMocks.request.validParams.defaultFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                }
            ],
        },
    },
};
