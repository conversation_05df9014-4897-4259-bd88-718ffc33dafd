import { PetSpeciesEnum } from "../../../enum/pet";
import { Questionaires } from "../../../entities/questionaires.entity";

export type QuestionaireResponse = {
    id?: string;
    type: string;
    species: PetSpeciesEnum;
    questionaires: Questionaires[];
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type QuestionaireListResponse = {
    rows: QuestionaireResponse[];
    count: number;
};
