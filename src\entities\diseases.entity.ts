import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { validated } from "../utils/validations";
import { FormatValidationError } from "../middlewares/errorHandler";
import { createConfig, receiveConfig } from "../validations/diseases/diseases.validate.config";
import { ResponseMessageEnum as m } from "../enum/response";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { DiseaseResponse } from "src/modules/diseases/dto/response";

export type DiseaseAttributes = {
    diseaseId: string;
    name: string;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
    deletedAt: Date | null;
};
export class DiseaseEntity {
    private readonly diseaseId: string;
    private readonly diseaseData: DiseaseAttributes;
    constructor(id: string | null, preData: DiseaseAttributes | null, customConfig?: Record<string, FieldConfigType>) {
        this.diseaseId = id!;
        this.diseaseData = this.initialize(preData, customConfig);
    }

    private initialize(
        preData: DiseaseAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): DiseaseAttributes {
        this.validate(preData, customConfig);
        return {
            diseaseId: this.diseaseId ?? uuidv7(),
            name: preData?.name,
            createdAt: preData?.createdAt,
            updatedAt: preData?.updatedAt,
            isDeleted: preData?.isDeleted,
            deletedAt: preData?.deletedAt,
        } as DiseaseAttributes;
    }
    private validate(preData: DiseaseAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateDisease(preData, customConfig);
        if (this.diseaseId) this.validateId(this.diseaseId);
    }
    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    private validateDisease(data: DiseaseAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? receiveConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    private validateDiseaseName(name: string): void {
        const schema = createConfig;
        const result = validated(schema, { name });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    public setDiseaseName(name: string): void {
        this.validateDiseaseName(name);
        this.diseaseData.name = name;
    }

    public getId(): string {
        return this.diseaseId;
    }
    public getAttributes(): DiseaseAttributes {
        return this.diseaseData;
    }
    public getResponse(): DiseaseResponse {
        return {
            diseaseId: this.diseaseId,
            name: this.diseaseData.name,
            createdAt: this.diseaseData.createdAt,
            updatedAt: this.diseaseData.updatedAt,
        };
    }
}
