import { OwnerInfo } from "../../../entities/owners.entity";
import { PetGenderTypeEnum, PetSpeciesEnum, PetLivingEnvironmentEnum } from "../../../enum/pet";
import { ActiveScore } from "../../../entities/pets.entity";

type PetWeightsRequest = {
    weight: number;
    measuredDate: string;
};

type DiseasesRequest = {
    diseaseId: string;
    diseaseName: string;
    validFrom: string;
    validTo: string;
    createdAt: string;
    updatedAt: string;
};

export type PetInfoRequest = {
    hn: string;
    name: string;
    gender: PetGenderTypeEnum;
    species: PetSpeciesEnum;
    breed: string;
    isMixedBreed: boolean;
    birthDate: string | null;
    weights: PetWeightsRequest[];
    livingEnvironment: PetLivingEnvironmentEnum;
    isNeutering: boolean;
    diseases: DiseasesRequest[];
    activeScore: ActiveScore;
};

export type PetRequest = {
    ownerId: string | null;
    petInfo: PetInfoRequest;
};

export type CreatePetWithOwnerRequest = {
    petDetails: {
        ownerId: string | null;
        petInfo: PetInfoRequest;
    };
    ownerDetails: {
        ownerInfo: OwnerInfo;
    } | null;
};

export type CalculateBCSScoreRequest = {
    scores: number[];
};

export type GetPetListFilterRequest = {
    petHnId?: string;
    petName?: string;
    ownerName?: string;
    ownerPhoneNumber?: string;
};
