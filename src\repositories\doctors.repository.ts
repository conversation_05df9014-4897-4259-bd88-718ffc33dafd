import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { DoctorAttributes } from "../entities/doctors.entity";
import { withMongoDB } from "../db/mongodb";
import { Db } from "mongodb";
import { DoctorFilter } from "../modules/doctors/dto/request";
import { DATABASE_TABLE } from "../constants/database";

type DoctorInfoKeys = keyof DoctorFilter;
type DoctorInfoSearchObject = {
    [K in DoctorInfoKeys]: { [Key in `doctorInfo.${K}`]: DoctorFilter[K] };
}[DoctorInfoKeys];
type Filter = {
    isDeleted: boolean;
    $and?: Array<DoctorInfoSearchObject>;
};
const DefaultFilter: Filter = {
    isDeleted: false,
};
export class DoctorRepository {
    async initCollection(db: Db) {
        return db.collection<DoctorAttributes>(DATABASE_TABLE.DOCTORS);
    }
    async getAll(): Promise<DoctorAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const collection = await this.initCollection(db);
                return collection.find(DefaultFilter).toArray();
            });
        } catch (error) {
            console.error(error);
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async create(data: DoctorAttributes): Promise<DoctorAttributes> {
        try {
            const doctorData: DoctorAttributes = {
                ...data,
                isDeleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                deletedAt: null,
            };
            const createdDoctor = await withMongoDB(async (db) => {
                const collection = await this.initCollection(db);
                return collection.insertOne(doctorData);
            });
            if (!createdDoctor) {
                throw new QueryError(m.INSERTION_FAILED);
            }

            return doctorData;
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async findDoctor(data: DoctorFilter): Promise<DoctorAttributes | null> {
        try {
            const andConditions: Filter["$and"] = [];

            if (data.phoneNumber) andConditions.push({ "doctorInfo.phoneNumber": data.phoneNumber });
            if (data.email) andConditions.push({ "doctorInfo.email": data.email });
            if (data.firstName) andConditions.push({ "doctorInfo.firstName": data.firstName });
            if (data.lastName) andConditions.push({ "doctorInfo.lastName": data.lastName });

            const filter: Filter = { ...DefaultFilter };
            if (andConditions.length > 0) filter.$and = andConditions;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);
                const doctor = await collection.findOne(filter);
                return doctor;
            });
        } catch (error) {
            console.error(error);
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async update(id: string, data: Partial<DoctorAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<DoctorAttributes>(DATABASE_TABLE.DOCTORS).updateOne(
                    { id: id, ...DefaultFilter },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw error;
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<DoctorAttributes>(DATABASE_TABLE.DOCTORS).updateOne(
                    { id: id, ...DefaultFilter },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw error;
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}
