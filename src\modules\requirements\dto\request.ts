import { PetSpeciesEnum } from "../../../enum/pet";
import { NutrientTypeEnum } from "../../../enum/requirement";

type NutrientDetailRange = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
};

export type RequirementSearchListRequest = {
    species?: PetSpeciesEnum;
    name?: string;
    limit: number;
    page: number;
};

export type RequirementRequest = {
    name: string;
    species: PetSpeciesEnum;
    nutrients: NutrientDetailRange[];
};
