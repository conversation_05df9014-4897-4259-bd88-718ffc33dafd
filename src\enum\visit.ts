enum VisitStatusEnum {
    WAITING = "waiting",
    INPROGRESS = "inprogress",
    CLOSED = "closed",
}

enum VisitStepEnum {
    STEP_ONE = "1",
    STEP_TWO = "2",
    STEP_THREE = "3",
    STEP_FOUR = "4",
    STEP_FIVE = "5",
    STEP_SIX = "6",
    STEP_SEVEN = "7",
}

enum PetAllergicEnum {
    NONE = "none",
    PORK = "pork",
    BEEF = "beef",
    CHICKEN = "chicken",
    FISH = "fish",
    DUCK = "duck",
    SHEEP = "sheep"
}

enum MappingAlgorithmEnum {
    MINMOSTFIT = "MinMostFit",
    MINLOWEST = "MinLowest",
}

export { VisitStatusEnum, VisitStepEnum, PetAllergicEnum, MappingAlgorithmEnum };