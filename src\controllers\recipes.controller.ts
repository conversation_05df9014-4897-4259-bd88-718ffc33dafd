import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { RecipeModule } from "../modules/recipes/recipes.module";
import { RecipeRequest, RecipeMatchRequest } from "../modules/recipes/dto/request";
import { ListLimitConstants } from "../constants/defaultValue";

export class RecipeController {
    private readonly recipeModule: RecipeModule;
    public router: Router;
    constructor(recipeModule: RecipeModule) {
        this.recipeModule = recipeModule;
        this.router = express.Router();
        this.routes();
    }

    private routes() {
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/match", this.findMatchingRecipes.bind(this));
        this.router.post("/bulk", this.createMultiple.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }

    private async findMatchingRecipes(req: Request, res: Response, next: NextFunction) {
        try {
            const body: RecipeMatchRequest = req.body;

            const response = await this.recipeModule.findMatchingRecipes(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.recipeModule.getOne(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const limit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            const page = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;
            const response = await this.recipeModule.getList(limit, page);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.recipeModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async createMultiple(req: Request, res: Response, next: NextFunction) {
        try {
            const body: RecipeRequest[] = req.body;
            const response = await this.recipeModule.createMultiple(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as RecipeRequest;
            const response = await this.recipeModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body;
            const response = await this.recipeModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.recipeModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
