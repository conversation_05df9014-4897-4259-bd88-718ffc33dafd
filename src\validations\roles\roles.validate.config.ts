import { FieldConfigMap } from "../../types/validate.type";
import { RoleValidateType } from "./roles.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<RoleValidateType> = {
    id: { type: v.UUID, required: false },
    name: { type: v.LETTER_ONLY, required: true },
};

export const receiveConfig: FieldConfigMap<RoleValidateType> = {
    id: { type: v.UUID, required: true },
    name: { type: v.LETTER_ONLY, required: true },
};
