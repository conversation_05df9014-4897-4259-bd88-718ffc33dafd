import { v4 as uuidv4 } from "uuid";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";
import { UserAttributes } from "../../../entities/users.entity";

const userAttributesOne = {
    id: uuidv4(),
    username: "admin",
    firstName: "john",
    lastName: "doe",
    email: "<EMAIL>",
    emailVerified: false,
    createdAt: new Date(),
    enabled: true,
};
const userAttributesTwo = {
    id: uuidv4(),
    username: "user",
    firstName: "robert",
    lastName: "smith",
    email: "<EMAIL>",
    emailVerified: false,
    createdAt: new Date(),
    enabled: true,
};
const roleAttributesOne = {
    id: uuidv4(),
    name: "admin",
};
const permissionAttributesOne = {
    id: uuidv4(),
    name: "read-user",
};

export const getAllMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
    },
    serviceResponse: {
        success: [
            {
                id: userAttributesOne.id,
                username: userAttributesOne.username,
                firstName: userAttributesOne.firstName,
                lastName: userAttributesOne.lastName,
                email: userAttributesOne.email,
                emailVerified: userAttributesOne.emailVerified,
                createdTimestamp: userAttributesOne.createdAt.getTime(),
                enabled: userAttributesOne.enabled,
            },
            {
                id: userAttributesTwo.id,
                username: userAttributesTwo.username,
                firstName: userAttributesTwo.firstName,
                lastName: userAttributesTwo.lastName,
                email: userAttributesTwo.email,
                emailVerified: userAttributesTwo.emailVerified,
                createdTimestamp: userAttributesTwo.createdAt.getTime(),
                enabled: userAttributesOne.enabled,
            },
        ],
        successWithoutDto: [
            {
                id: userAttributesOne.id,
                username: userAttributesOne.username,
                firstName: userAttributesOne.firstName,
                lastName: userAttributesOne.lastName,
                email: userAttributesOne.email,
                emailVerified: userAttributesOne.emailVerified,
                createdTimestamp: userAttributesOne.createdAt.getTime(),
                enabled: userAttributesOne.enabled,
                somethingElse: "value",
            },
            {
                id: userAttributesTwo.id,
                username: userAttributesTwo.username,
                firstName: userAttributesTwo.firstName,
                lastName: userAttributesTwo.lastName,
                email: userAttributesTwo.email,
                emailVerified: userAttributesTwo.emailVerified,
                createdTimestamp: userAttributesTwo.createdAt.getTime(),
                enabled: userAttributesOne.enabled,
                somethingElse: "value",
            },
        ],
    },
    response: {
        success: [
            {
                ...userAttributesOne,
            },
            {
                ...userAttributesTwo,
            },
        ],
        empty: [],
        invalidKey: [
            {
                somethingElse: "value",
            },
        ],
        invalidValue: [
            {
                id: "1",
                username: "*{}",
                firstName: "3",
                lastName: "4",
                email: "5",
            },
        ],
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_USER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                username: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                email: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
            },
        },
    },
};

export const getOneMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
    },
    serviceResponse: {
        success: {
            id: userAttributesOne.id,
            username: userAttributesOne.username,
            firstName: userAttributesOne.firstName,
            lastName: userAttributesOne.lastName,
            email: userAttributesOne.email,
            emailVerified: userAttributesOne.emailVerified,
            createdTimestamp: userAttributesOne.createdAt.getTime(),
            enabled: userAttributesOne.enabled,
        },
        successWithoutDto: {
            id: userAttributesOne.id,
            username: userAttributesOne.username,
            firstName: userAttributesOne.firstName,
            lastName: userAttributesOne.lastName,
            email: userAttributesOne.email,
            emailVerified: userAttributesOne.emailVerified,
            createdTimestamp: userAttributesOne.createdAt.getTime(),
            enabled: userAttributesOne.enabled,
            somethingElse: "value",
        },
    },
    response: {
        success: {
            ...userAttributesOne,
        },
        invalidKey: {
            somethingElse: "value",
        },
        invalidValue: {
            id: "1",
            username: "*{}",
            firstName: "3",
            lastName: "4",
            email: "5",
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ONE_USER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                username: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                email: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
            },
        },
    },
};

export const getListMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validLimit: 10,
        validLimitOne: 1,
        validPage: 1,
        noneFilter: {},
        validFilter: {
            username: userAttributesOne.username,
            firstName: userAttributesOne.firstName,
            lastName: userAttributesOne.lastName,
            email: userAttributesOne.email,
        },
        invalidFilter: {
            username: "*{}",
            firstName: "3",
            lastName: "4",
            email: "5",
        },
    },
    serviceResponse: {
        success: getAllMocks.serviceResponse.success,
        successWithoutDto: getAllMocks.serviceResponse.successWithoutDto,
        successWithPagination: [
            {
                id: userAttributesOne.id,
                username: userAttributesOne.username,
                firstName: userAttributesOne.firstName,
                lastName: userAttributesOne.lastName,
                email: userAttributesOne.email,
                emailVerified: userAttributesOne.emailVerified,
                createdTimestamp: userAttributesOne.createdAt.getTime(),
                enabled: userAttributesOne.enabled,
            },
        ],
    },
    response: {
        success: getAllMocks.response.success,
        successWithPagination: [
            {
                ...userAttributesOne,
            },
        ],
        empty: [],
        invalidKey: getAllMocks.response.invalidKey,
        invalidValue: getAllMocks.response.invalidValue,
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_LIST_USER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidFilterError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                email: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
            },
        },
    },
};

export const createMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validBody: {
            username: "admin",
            firstName: "john",
            lastName: "doe",
            email: "<EMAIL>",
        },
        validBodyWithRoles: {
            username: "admin",
            firstName: "john",
            lastName: "doe",
            email: "<EMAIL>",
            roles: ["admin"],
        },
        invalidBodyValue: {
            username: "*{}",
            firstName: "3",
            lastName: "4",
            email: "5",
            roles: ["admin", "*{}"],
        },
        missingBodyKey: {
            somethingElse: "value",
        } as any as UserAttributes,
    },
    serviceResponse: {
        success: undefined,
    },
    response: {
        success: {
            username: "admin",
            password: "mockedPassword",
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_USER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                email: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
                roles: [{ _error: v.LETTER_ONLY }],
            },
        },
        invalidCredentialsError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                value: v.REQUIRED,
            },
        },
        missingCredentialsError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                type: v.REQUIRED,
                value: v.REQUIRED,
            },
        },
    },
};

export const updateMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
        validBody: {
            username: "admin",
            firstName: "john",
            lastName: "doe",
            email: "<EMAIL>",
            roles: ["admin"],
        },
        validBodyWithRole: {
            username: "admin",
            firstName: "john",
            lastName: "doe",
            email: "<EMAIL>",
            roles: ["admin"],
        },
        invalidBodyValue: {
            username: "*{}",
            firstName: "3",
            lastName: "4",
            email: "5",
            roles: ["admin", "*{}"],
        },
        missingBodyKey: {
            somethingElse: "value",
        } as any as UserAttributes,
    },
    response: {
        success: {
            data: null,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.UPDATE_USER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                email: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
                roles: [{ _error: v.LETTER_ONLY }],
            },
        },
        invalidAllError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                username: v.NO_PROHIBITED_CHARACTERS,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                email: v.EMAIL,
                roles: [{ _error: v.LETTER_ONLY }],
            },
        },
    },
};

export const deleteMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
    },
    response: {
        success: {
            data: null,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.DELETE_USER_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
    },
};

export const getRolesMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
    },
    response: {
        success: [
            {
                id: roleAttributesOne.id,
                name: roleAttributesOne.name,
            },
        ],
        empty: [],
        invalidRoleValue: [
            {
                id: "1",
                name: "2",
            },
        ],
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_USER_ROLES_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        invalidRoleError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.LETTER_ONLY,
            },
        },
    },
};

export const getPermissionsMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        invalidParams: {
            id: "1",
        },
    },
    response: {
        success: [
            {
                id: permissionAttributesOne.id,
                name: permissionAttributesOne.name,
            },
        ],
        empty: [],
        invalidPermissionValue: [
            {
                id: "1",
                name: "*{}",
            },
        ],
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_USER_PERMISSIONS_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        invalidPermissionError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
    },
};

export const addPermissionMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        notFoundParams: {
            id: uuidv4(),
        },
        invalidParams: {
            id: "1",
        },
        validBody: {
            id: permissionAttributesOne.id,
            name: permissionAttributesOne.name,
        },
        invalidBody: {
            id: "1",
            name: "*{}",
        },
    },
    response: {
        success: {
            data: null,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.ADD_USER_PERMISSION_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        invalidPermissionError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
    },
};

export const removePermissionMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            id: userAttributesOne.id,
        },
        notFoundParams: {
            id: uuidv4(),
        },
        invalidParams: {
            id: "1",
        },
        validBody: {
            id: permissionAttributesOne.id,
            name: permissionAttributesOne.name,
        },
        invalidBody: {
            id: "1",
            name: "*{}",
        },
    },
    response: {
        success: {
            data: null,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.REMOVE_USER_PERMISSION_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidUuidError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
        invalidPermissionError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
    },
};
