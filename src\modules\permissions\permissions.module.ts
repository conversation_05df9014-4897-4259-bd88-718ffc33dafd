import { KeycloakService } from "../../services/keycloak.service";
import { PermissionsResponse, PermissionsListResponse } from "./dto/response";
import { PermissionsRequest } from "./dto/request";
import { ServiceError, DefaultError, FormatValidationError } from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { PermissionEntity, PermissionAttributes } from "../../entities/permissions.entity";
import { receiveConfig } from "../../validations/permissions/permissions.validate.config";

export class PermissionModule {
    private authService!: KeycloakService;
    private initializedAuthService(token: string): void {
        try {
            this.authService = new KeycloakService(token);
        } catch (error) {
            console.error(e.SERVICE_ERROR, error);
            throw new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_CONFIGURED_ERROR);
        }
    }
    async getAll(token: string): Promise<PermissionsResponse[]> {
        try {
            this.initializedAuthService(token);
            const clientRoles = await this.authService.getClientRoles();
            return clientRoles.map((role) => {
                const permissionEntity = new PermissionEntity(role.name, role, receiveConfig);
                const permissionResponse = permissionEntity.getPermissionAttributes();
                return permissionResponse;
            });
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_PERMISSIONS_FAILED);
        }
    }
    async getOne(token: string, name: string): Promise<PermissionsResponse> {
        try {
            this.initializedAuthService(token);
            const permissionEntity = new PermissionEntity(name, null);
            const permissionName = permissionEntity.getName();
            const permission = await this.authService.getClientRoleByName(permissionName);
            permissionEntity.setAttributes(permission, receiveConfig);
            const permissionResponse = permissionEntity.getPermissionAttributes();
            return permissionResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_PERMISSION_FAILED);
        }
    }
    async getList(token: string, limit: number, page: number): Promise<PermissionsListResponse> {
        try {
            this.initializedAuthService(token);
            const clientRoles = await this.authService.getClientRoles();
            const permissionsResponse = clientRoles.map((role) => {
                const permissionEntity = new PermissionEntity(role.name, role, receiveConfig);
                const permissionResponse = permissionEntity.getPermissionAttributes();
                return permissionResponse;
            });
            // pagination
            let offset: number = 0;
            let permissionsResponseWithPagination = permissionsResponse;
            if (limit && page) {
                offset = limit * (page - 1);
                permissionsResponseWithPagination = permissionsResponse.slice(offset, limit * page);
            }
            const permissionsListResponse = {
                rows: permissionsResponseWithPagination,
                count: permissionsResponse.length,
            };
            return permissionsListResponse as PermissionsListResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_PERMISSIONS_FAILED);
        }
    }
    async create(token: string, data: PermissionsRequest): Promise<void> {
        try {
            const permissionEntity = new PermissionEntity(null, data as PermissionAttributes);
            this.initializedAuthService(token);
            const permissionData = permissionEntity.getPermissionRepresentation();
            await this.authService.createClientRole(permissionData);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_PERMISSION_FAILED);
        }
    }
    async update(token: string, name: string, data: PermissionsRequest): Promise<void> {
        try {
            const permissionEntity = new PermissionEntity(name, data as PermissionAttributes);
            this.initializedAuthService(token);
            const permissionName = permissionEntity.getName();
            const permissionData = permissionEntity.getPermissionRepresentation();
            await this.authService.updateClientRole(permissionName, permissionData);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_PERMISSION_FAILED);
        }
    }
    async delete(token: string, name: string): Promise<void> {
        try {
            const permissionEntity = new PermissionEntity(name, null);
            this.initializedAuthService(token);
            const permissionName = permissionEntity.getName();
            await this.authService.deleteClientRole(permissionName);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_PERMISSION_FAILED);
        }
    }
}
