import { v7 as uuidv7 } from "uuid";
import { TestCase } from "../../utils/testRunner";
import { RequirementModule } from "../../../modules/requirements/requirements.module";

import {
    getOneMocks,
    getListMocks,
    getAllMocks,
    getAllBySpeciesMocks,
    createMultipleMocks,
    createMocks,
    updateMocks,
    deleteMocks,
} from "./requirements.test.mock";
import { DefaultError, RepositoryError } from "../../../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m, ResponseTitleEnum as r } from "../../../enum/response";
import { RequirementRequest, RequirementSearchListRequest } from "../../../modules/requirements/dto/request";
import {
    RequirementResponse,
    RequirementNameResponse,
    RequirementListResponse,
} from "../../../modules/requirements/dto/response";
import { PetSpeciesEnum } from "../../../enum/pet";

type MethodTypeMapper = {
    getOne: { input: { id: string }; output: RequirementResponse };
    getList: { input: { filter: RequirementSearchListRequest }; output: RequirementListResponse };
    getAll: { input: null; output: RequirementResponse[] };
    getAllBySpecies: { input: { species: PetSpeciesEnum }; output: RequirementNameResponse[] };
    createMultiple: { input: { data: RequirementRequest[] }; output: { insertedCount: number } };
    create: { input: { data: RequirementRequest }; output: RequirementResponse };
    update: { input: { id: string; data: RequirementRequest }; output: boolean };
    delete: { input: { id: string }; output: boolean };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof RequirementModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

// Mock repository object
export const mockRepository = {
    findRequirementById: jest.fn(),
    findRequirementByName: jest.fn(),
    findList: jest.fn(),
    findAll: jest.fn(),
    findExistingRequirementByNameAndSpecies: jest.fn(),
    findAllRequirementsBySpecies: jest.fn(),
    createRequirements: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
};

// Replace the real Repository class with our mock version
jest.mock("../../../repositories/requirements.repository", () => ({
    RequirementRepository: jest.fn(() => mockRepository),
}));

const moduleInstance = new RequirementModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Requirement",
    testCases: {
        getOne: {
            cases: [
                {
                    name: "[SUCCESS] should return requirement details when found by id",
                    setup: {
                        input: {
                            id: getOneMocks.request.validParams.validUUID,
                        },
                        mocks: (): void => {
                            mockRepository.findRequirementById.mockResolvedValue(getOneMocks.response.validUUID);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.id);
                    },
                    expectation: {
                        result: getOneMocks.response.validUUID,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findRequirementById,
                                called: true,
                                withArgs: [getOneMocks.response.validUUID.id],
                            },
                        ],
                        additionalChecks: {
                            onSuccess: (result) => {
                                expect(result).not.toBeNull();
                            },
                        },
                    },
                },
                {
                    name: "[FAILED] should throw validation error when id is not a valid UUID",
                    setup: {
                        input: {
                            id: getOneMocks.request.invalidParams.invalidUUID,
                        },
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidUUID,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findRequirementById,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw not found error when requirement id is not found",
                    setup: {
                        input: {
                            id: getOneMocks.request.validParams.validUUID,
                        },
                        mocks: (): void => {
                            const error = new RepositoryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                            mockRepository.findRequirementById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findRequirementById,
                                called: true,
                                withArgs: [getOneMocks.request.validParams.validUUID],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            id: getOneMocks.request.validParams.validUUID,
                        },
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.findRequirementById.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findRequirementById,
                                called: true,
                                withArgs: [getOneMocks.request.validParams.validUUID],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return paginated list of requirements with filtering by species and name",
                    setup: {
                        input: {
                            filter: getListMocks.request.validParams.nameAndSpecies,
                        },
                        mocks: (): void => {
                            mockRepository.findList.mockResolvedValue(getListMocks.response.nameAndSpecies);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.nameAndSpecies,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findList,
                                called: true,
                                withArgs: [expect.objectContaining(getListMocks.request.validParams.nameAndSpecies)],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return paginated list of requirements with filtering by only species",
                    setup: {
                        input: {
                            filter: getListMocks.request.validParams.speciesOnly,
                        },
                        mocks: (): void => {
                            mockRepository.findList.mockResolvedValue(getListMocks.response.speciesOnly);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.speciesOnly,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findList,
                                called: true,
                                withArgs: [expect.objectContaining(getListMocks.request.validParams.speciesOnly)],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return paginated list of requirements with filtering by only name",
                    setup: {
                        input: {
                            filter: getListMocks.request.validParams.nameOnly,
                        },
                        mocks: (): void => {
                            mockRepository.findList.mockResolvedValue(getListMocks.response.nameOnly);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.nameOnly,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findList,
                                called: true,
                                withArgs: [expect.objectContaining(getListMocks.request.validParams.nameOnly)],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no requirements exist with filter",
                    setup: {
                        input: {
                            filter: getListMocks.request.validParams.nameAndSpecies,
                        },
                        mocks: (): void => {
                            mockRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findList,
                                called: true,
                                withArgs: [expect.objectContaining(getListMocks.request.validParams.nameAndSpecies)],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            filter: getListMocks.request.validParams.nameAndSpecies,
                        },
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.findList.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findList,
                                called: true,
                                withArgs: [expect.objectContaining(getListMocks.request.validParams.nameAndSpecies)],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: {
                            filter: getListMocks.request.validParams.nameAndSpecies,
                        },
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.findList.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findList,
                                called: true,
                                withArgs: [expect.objectContaining(getListMocks.request.validParams.nameAndSpecies)],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all requirements",
                    setup: {
                        input: null,
                        mocks: (): void => {
                            mockRepository.findAll.mockResolvedValue(getAllMocks.response.success);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no requirements exist",
                    setup: {
                        input: null,
                        mocks: (): void => {
                            mockRepository.findAll.mockResolvedValue(getAllMocks.response.empty);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: null,
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.findAll.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: null,
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.findAll.mockRejectedValue(error);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getAllBySpecies: {
            cases: [
                {
                    name: "[SUCCESS] should return all requirements by species",
                    setup: {
                        input: {
                            species: getAllBySpeciesMocks.request.validParams.validSpecies,
                        },
                        mocks: (): void => {
                            mockRepository.findAllRequirementsBySpecies.mockResolvedValue(
                                getAllBySpeciesMocks.response.validSpecies
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAllBySpecies(input.species);
                    },
                    expectation: {
                        result: getAllBySpeciesMocks.response.validSpecies,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findAllRequirementsBySpecies,
                                called: true,
                                withArgs: [getAllBySpeciesMocks.request.validParams.validSpecies],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no requirements exist for the species",
                    setup: {
                        input: {
                            species: getAllBySpeciesMocks.request.validParams.validSpecies,
                        },
                        mocks: (): void => {
                            mockRepository.findAllRequirementsBySpecies.mockResolvedValue(
                                getAllBySpeciesMocks.response.empty
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAllBySpecies(input.species);
                    },
                    expectation: {
                        result: getAllBySpeciesMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findAllRequirementsBySpecies,
                                called: true,
                                withArgs: [getAllBySpeciesMocks.request.validParams.validSpecies],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            species: getAllBySpeciesMocks.request.validParams.validSpecies,
                        },
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.findAllRequirementsBySpecies.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAllBySpecies(input.species);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllBySpeciesMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findAllRequirementsBySpecies,
                                called: true,
                                withArgs: [getAllBySpeciesMocks.request.validParams.validSpecies],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: {
                            species: getAllBySpeciesMocks.request.validParams.validSpecies,
                        },
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.findAllRequirementsBySpecies.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAllBySpecies(input.species);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllBySpeciesMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findAllRequirementsBySpecies,
                                called: true,
                                withArgs: [getAllBySpeciesMocks.request.validParams.validSpecies],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        createMultiple: {
            cases: [
                {
                    name: "[SUCCESS] should successfully create multiple requirements",
                    setup: {
                        input: {
                            data: createMultipleMocks.request.validParams.validMultipleData,
                        },
                        mocks: (): void => {
                            jest.spyOn(
                                require("../../../entities/requirements.entity"),
                                "RequirementEntity"
                            ).mockImplementation((id, preData) => ({
                                getAttributes: () => ({
                                    ...(preData ?? {}),
                                    id: id ?? uuidv7(),
                                }),
                            }));
                            mockRepository.createRequirements.mockResolvedValue(
                                createMultipleMocks.response.validMultipleData
                            );
                        },
                        teardown: () => {
                            jest.restoreAllMocks();
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createMultiple(input.data);
                    },
                    expectation: {
                        result: createMultipleMocks.response.validMultipleData,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.createRequirements,
                                called: true,
                                withArgs: [
                                    expect.arrayContaining([
                                        expect.objectContaining({
                                            id: expect.any(String),
                                            ...createMultipleMocks.request.validParams.validMultipleData[0],
                                        }),
                                        expect.objectContaining({
                                            id: expect.any(String),
                                            ...createMultipleMocks.request.validParams.validMultipleData[1],
                                        }),
                                    ]),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error when min is not a number",
                    setup: {
                        input: {
                            data: createMultipleMocks.request.invalidParams.invalidMin,
                        },
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.createMultiple(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMultipleMocks.error.invalidMin,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.createRequirements,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error when species is not a valid string",
                    setup: {
                        input: {
                            data: createMultipleMocks.request.invalidParams.invalidSpecies,
                        },
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.createMultiple(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMultipleMocks.error.invalidSpecies,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.createRequirements,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            data: createMultipleMocks.request.validParams.validMultipleData,
                        },
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.createRequirements.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createMultiple(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMultipleMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.createRequirements,
                                called: true,
                                withArgs: [
                                    expect.arrayContaining([
                                        expect.objectContaining({
                                            id: expect.any(String),
                                            ...createMultipleMocks.request.validParams.validMultipleData[0],
                                        }),
                                        expect.objectContaining({
                                            id: expect.any(String),
                                            ...createMultipleMocks.request.validParams.validMultipleData[1],
                                        }),
                                    ]),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: {
                            data: createMultipleMocks.request.validParams.validMultipleData,
                        },
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.createRequirements.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.createMultiple(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMultipleMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.createRequirements,
                                called: true,
                                withArgs: [
                                    expect.arrayContaining([
                                        expect.objectContaining({
                                            id: expect.any(String),
                                            ...createMultipleMocks.request.validParams.validMultipleData[0],
                                        }),
                                        expect.objectContaining({
                                            id: expect.any(String),
                                            ...createMultipleMocks.request.validParams.validMultipleData[1],
                                        }),
                                    ]),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should successfully create requirement",
                    setup: {
                        input: {
                            data: createMocks.request.validParams.validData,
                        },
                        mocks: (): void => {
                            jest.spyOn(
                                require("../../../entities/requirements.entity"),
                                "RequirementEntity"
                            ).mockImplementation((id, preData) => ({
                                getAttributes: () => ({
                                    ...(preData ?? {}),
                                    id: id ?? uuidv7(),
                                }),
                            }));
                            mockRepository.findExistingRequirementByNameAndSpecies.mockResolvedValue(null);
                            mockRepository.create.mockResolvedValue(createMocks.response.validData);
                        },
                        teardown: () => {
                            jest.restoreAllMocks();
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: createMocks.response.validData,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.findExistingRequirementByNameAndSpecies,
                                called: true,
                                withArgs: [
                                    createMocks.request.validParams.validData.name,
                                    createMocks.request.validParams.validData.species,
                                ],
                            },
                            {
                                method: mockRepository.create,
                                called: true,
                                withArgs: [
                                    expect.objectContaining({
                                        id: expect.any(String),
                                        ...createMocks.request.validParams.validData,
                                    }),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error when min is not a number",
                    setup: {
                        input: {
                            data: createMocks.request.invalidParams.invalidMin,
                        },
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidMin,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findExistingRequirementByNameAndSpecies,
                                called: false,
                            },
                            {
                                method: mockRepository.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error when species is not a valid string",
                    setup: {
                        input: {
                            data: createMocks.request.invalidParams.invalidSpecies,
                        },
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidSpecies,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findExistingRequirementByNameAndSpecies,
                                called: false,
                            },
                            {
                                method: mockRepository.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validParams.validData,
                        },
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.findExistingRequirementByNameAndSpecies.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findExistingRequirementByNameAndSpecies,
                                called: true,
                                withArgs: [
                                    createMocks.request.validParams.validData.name,
                                    createMocks.request.validParams.validData.species,
                                ],
                            },
                            {
                                method: mockRepository.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validParams.validData,
                        },
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.findExistingRequirementByNameAndSpecies.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.findExistingRequirementByNameAndSpecies,
                                called: true,
                                withArgs: [
                                    createMocks.request.validParams.validData.name,
                                    createMocks.request.validParams.validData.species,
                                ],
                            },
                            {
                                method: mockRepository.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        update: {
            cases: [
                {
                    name: "[SUCCESS] should successfully update requirement",
                    setup: {
                        input: updateMocks.request.validParams.validData,
                        mocks: (): void => {
                            mockRepository.update.mockResolvedValue(true);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: true,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: true,
                                withArgs: [
                                    updateMocks.request.validParams.validData.id,
                                    expect.objectContaining(updateMocks.request.validParams.validData.data),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error when id is not a valid UUID",
                    setup: {
                        input: updateMocks.request.invalidParams.invalidUUIDData,
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidUUID,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: updateMocks.request.validParams.validData,
                        mocks: () => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.update.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: true,
                                withArgs: [
                                    updateMocks.request.validParams.validData.id,
                                    expect.objectContaining(updateMocks.request.validParams.validData.data),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: updateMocks.request.validParams.validData,
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.update.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: true,
                                withArgs: [
                                    updateMocks.request.validParams.validData.id,
                                    expect.objectContaining(updateMocks.request.validParams.validData.data),
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        delete: {
            cases: [
                {
                    name: "[SUCCESS] should successfully delete requirement",
                    setup: {
                        input: {
                            id: deleteMocks.request.validParams.validUUID,
                        },
                        mocks: (): void => {
                            mockRepository.update.mockResolvedValue(deleteMocks.response.validUUID);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: true,
                        error: null,
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: true,
                                withArgs: [
                                    deleteMocks.request.validParams.validUUID,
                                    {
                                        isDeleted: true,
                                        deletedAt: expect.any(Date),
                                    },
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error when id is not a valid UUID",
                    setup: {
                        input: {
                            id: deleteMocks.request.invalidParams.invalidUUID,
                        },
                        mocks: null,
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidUUID,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            id: deleteMocks.request.validParams.validUUID,
                        },
                        mocks: (): void => {
                            const unexpectedError = new Error(r.UNEXPECTED_ERROR);
                            mockRepository.update.mockRejectedValue(unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectedModuleError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: true,
                                withArgs: [
                                    deleteMocks.request.validParams.validUUID,
                                    {
                                        isDeleted: true,
                                        deletedAt: expect.any(Date),
                                    },
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw database error when database error occurs",
                    setup: {
                        input: {
                            id: deleteMocks.request.validParams.validUUID,
                        },
                        mocks: (): void => {
                            const error = new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
                            mockRepository.update.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectRepositoryError,
                        },
                        methodCalls: [
                            {
                                method: mockRepository.update,
                                called: true,
                                withArgs: [
                                    deleteMocks.request.validParams.validUUID,
                                    {
                                        isDeleted: true,
                                        deletedAt: expect.any(Date),
                                    },
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
