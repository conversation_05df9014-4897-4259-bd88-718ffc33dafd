import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig } from "../validations/breeds/breeds.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { PetSpeciesEnum, DogBreedEnum, CatBreedEnum } from "../enum/pet";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";

export type BreedAttributes = {
    id: string;
    species: PetSpeciesEnum;
    breedName: DogBreedEnum | CatBreedEnum;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export class BreedEntity {
    private readonly id: string | null;
    private readonly breedData: BreedAttributes;
    constructor(
        id: string | null = null, 
        preData: BreedAttributes | null = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.breedData = this.initialize(preData, customConfig);
    }

    private initialize(preData: BreedAttributes | null, customConfig?: Record<string, FieldConfigType>
    ): BreedAttributes {
        try {
            this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                species: preData?.species,
                breedName: preData?.breedName,
            } as BreedAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: BreedAttributes | null, customConfig?: Record<string, FieldConfigType>
    ): void {
        if (preData) this.validateBreedData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateBreedData(data: BreedAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.breedData.id;
    }

    public getAttributes(): BreedAttributes {
        return this.breedData;
    }
}
