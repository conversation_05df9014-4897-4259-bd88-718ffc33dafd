import { PetSpeciesEnum, Dog<PERSON>reed<PERSON><PERSON>, CatBreedEnum } from "../../../enum/pet";

export type BreedResponse = {
    id?: string;
    species: PetSpeciesEnum;
    breedName: DogBreedEnum | CatBreedEnum;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type BreedListResponse = {
    rows: BreedResponse[];
    count: number;
};
