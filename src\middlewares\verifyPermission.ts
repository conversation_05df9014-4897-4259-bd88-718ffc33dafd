import { Request, Response, NextFunction } from "express";
import { ResponseMessageEnum as m } from "../enum/response";
import { ErrorTypeEnum as e } from "../enum/errors";
import { KeycloakService } from "../services/keycloak.service";
import { DefaultError } from "./errorHandler";
//verify permission
export const requirePermission = (permission: string) => {
    return async (req: Request, _: Response, next: NextFunction) => {
        try {
            const decoded = req.body.user;
            let authService!: KeycloakService;
            try {
                authService = new KeycloakService("");
            } catch (error) {
                console.error(e.SERVICE_ERROR, error);
                return next(new DefaultError(e.SERVICE_ERROR, m.SERVICE_CONFIGURED_ERROR));
            }
            const userPermissions = authService.extractRoles(decoded);
            if (!userPermissions.includes(permission)) {
                console.log(`User ${decoded.preferred_username} accessing ${req.originalUrl}`);
                return next(new DefaultError(e.FORBIDDEN_ERROR, m.FORBIDDEN));
            }
            next();
        } catch (error) {
            if (error instanceof DefaultError) {
                return next(error);
            }
            return next(new DefaultError(e.INTERNAL_SERVER_ERROR, m.INTERNAL_SERVER_ERROR));
        }
    };
};
