import { RawMatAttributes } from "../../../entities/rawmats.entity";
import { RawmatRequest } from "../../../modules/rawmats/dto/request";
import { v7 as uuidv7 } from "uuid";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";
import { RawmatTypeEnum as t } from "../../../enum/rawmat";
import { NutrientTypeEnum as n } from "../../../enum/requirement";

const ValidRawmatBody: RawmatRequest = {
    name: "Rawmat",
    nutrients: [
        {
            nutrientName: "nutrientName",
            unit: "%",
            type: "ProximateAnalysis",
            value: 0,
        },
    ],
    type: "Carbohydrate",
    costPerGram: 0,
    mePerGram: 0,
};
const MaxName =
    "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.";
const MaxNumber = 9999999;
const InvalidRawmatBody: Record<string, RawmatRequest> = {
    invalidMin: {
        name: "",
        nutrients: [{ nutrientName: "", unit: "", type: n.PROXIMATE_ANALYSIS, value: -1 }],
        type: t.FAT,
        costPerGram: -1,
        mePerGram: -1,
    },
    invalidMax: {
        name: MaxName,
        nutrients: [{ nutrientName: MaxName, unit: MaxName, type: n.PROXIMATE_ANALYSIS, value: MaxNumber }],
        type: t.FAT,
        costPerGram: MaxNumber,
        mePerGram: MaxNumber,
    },
    invalidValue: {
        name: "$*@",
        nutrients: [{ nutrientName: "$*@", unit: 1, type: "fff", value: "0" }],
        type: "fff",
        costPerGram: "0",
        mePerGram: "0",
    } as unknown as RawmatRequest,
    invalidType: { name: 1, nutrients: "", type: 1, costPerGram: "0", mePerGram: "0" } as unknown as RawmatRequest,
    invalidKey: { somethingElse: "value" } as any as RawmatRequest,
};
const ValidRawMatAttributes: RawMatAttributes = {
    rawMatId: uuidv7(),
    name: "Rawmat",
    nutrients: [
        {
            nutrientName: "nutrientName",
            unit: "unit",
            type: n.PROXIMATE_ANALYSIS,
            value: 0,
        },
    ],
    type: t.CARBOHYDRATE,
    costPerGram: 0,
    mePerGram: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
};
const ValidRawMatAttributesDuplicate: RawMatAttributes = {
    rawMatId: uuidv7(),
    name: "Rawmat",
    nutrients: [
        {
            nutrientName: "nutrientName",
            unit: "unit",
            type: n.PROXIMATE_ANALYSIS,
            value: 0,
        },
    ],
    type: t.CARBOHYDRATE,
    costPerGram: 0,
    mePerGram: 0,
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
};
const InvalidRawmatAttributes = {
    empty: {},
    invalidValue: {
        rawMatId: true,
        name: "$*",
        nutrients: [
            {
                nutrientName: "$*",
                unit: 2,
                type: "",
                value: "",
            },
        ],
        type: "",
        costPerGram: "",
        mePerGram: "",
        createdAt: "",
        updatedAt: "",
        isDeleted: "",
        deletedAt: "",
    },
    invalidKey: {
        somethingElse: "value",
    } as any as RawMatAttributes,
};

export const getAllMocks = {
    serviceResponse: {
        success: {
            data: [ValidRawMatAttributes],
        },
        empty: {
            data: [],
        },
        missingKey: {
            data: [InvalidRawmatAttributes.invalidKey],
        },
        invalidValue: {
            data: [InvalidRawmatAttributes.invalidValue],
        },
    },
    response: {
        success: {
            data: [
                {
                    rawMatId: ValidRawMatAttributes.rawMatId,
                    name: ValidRawMatAttributes.name,
                    nutrients: ValidRawMatAttributes.nutrients,
                    type: ValidRawMatAttributes.type,
                    costPerGram: ValidRawMatAttributes.costPerGram,
                    mePerGram: ValidRawMatAttributes.mePerGram,
                    createdAt: ValidRawMatAttributes.createdAt,
                    updatedAt: ValidRawMatAttributes.updatedAt,
                },
            ],
        },
        empty: {
            data: [],
        },
    },
    error: {
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_RAWMATS_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        missingKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                rawMatId: v.REQUIRED,
                name: v.REQUIRED,
                nutrients: v.REQUIRED,
                type: v.REQUIRED,
                costPerGram: v.REQUIRED,
                mePerGram: v.REQUIRED,
                createdAt: v.REQUIRED,
                updatedAt: v.REQUIRED,
                isDeleted: v.REQUIRED,
                deletedAt: v.REQUIRED,
            },
        },
        invalidValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
                nutrients: [
                    {
                        nutrientName: v.NO_PROHIBITED_CHARACTERS,
                        unit: v.STRING,
                        type: v.ENUM,
                        value: v.NUMBER,
                    },
                ],
                type: v.ENUM,
                costPerGram: v.NUMBER,
                mePerGram: v.NUMBER,
                createdAt: v.DATE,
                updatedAt: v.DATE,
                isDeleted: v.BOOLEAN,
                deletedAt: v.DATE,
            },
        },
    },
};
export const createMocks = {
    request: {
        validBody: {
            ...ValidRawmatBody,
        },
        invalidBodyValue: {
            ...InvalidRawmatBody.invalidValue,
        },
        missingBodyKey: {
            ...InvalidRawmatBody.invalidKey,
        },
        emptyBody: {},
        invalidBodyMin: {
            ...InvalidRawmatBody.invalidMin,
        },
        invalidBodyMax: {
            ...InvalidRawmatBody.invalidMax,
        },
        invalidBodyType: {
            ...InvalidRawmatBody.invalidType,
        },
    },
    serviceResponse: {
        success: {
            data: void 0,
        },
        empty: {
            data: null,
        },
        duplicateName: {
            data: { ...ValidRawMatAttributesDuplicate },
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidBodyValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
                nutrients: [
                    {
                        nutrientName: v.NO_PROHIBITED_CHARACTERS,
                        unit: v.STRING,
                        type: v.ENUM,
                        value: v.NUMBER,
                    },
                ],
                type: v.ENUM,
                costPerGram: v.NUMBER,
                mePerGram: v.NUMBER,
            },
        },
        invalidBodyType: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.STRING,
                nutrients: v.ARRAY,
                type: v.STRING,
                costPerGram: v.NUMBER,
                mePerGram: v.NUMBER,
            },
        },
        invalidBodyMinLength: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: { _error: v.MIN_CHAR, _min: 2 },
                nutrients: [
                    {
                        nutrientName: { _error: v.MIN_CHAR, _min: 2 },
                        unit: { _error: v.MIN_CHAR, _min: 1 },
                        value: { _error: v.MIN_NUMBER, _min: 0 },
                    },
                ],
                costPerGram: { _error: v.MIN_NUMBER, _min: 0 },
                mePerGram: { _error: v.MIN_NUMBER, _min: 0 },
            },
        },
        invalidBodyMaxLength: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: { _error: v.MAX_CHAR, _max: 80 },
                nutrients: [
                    {
                        nutrientName: { _error: v.MAX_CHAR, _max: 80 },
                        unit: { _error: v.MAX_CHAR, _max: 20 },
                        value: { _error: v.MAX_NUMBER, _max: 999999 },
                    },
                ],
            },
        },
        missingBodyRequiredKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
                nutrients: v.REQUIRED,
                type: v.REQUIRED,
                costPerGram: v.REQUIRED,
                mePerGram: v.REQUIRED,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_RAWMAT_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        duplicateName: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
            },
            statusCode: s.CONFLICT,
        },
    },
};
