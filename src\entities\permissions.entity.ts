import { validated } from "../utils/validations";
import { createConfig } from "../validations/permissions/permissions.validate.config";
import { ResponseMessageEnum as m } from "../enum/response";
import { FormatValidationError } from "../middlewares/errorHandler";
import { FieldConfigType } from "../types/validate.type";
import { ClientRoleRepresentation } from "../services/keycloak.service";
import { PermissionsResponse } from "../modules/permissions/dto/response";

export type PermissionAttributes = {
    id: string;
    name: string;
    description?: string;
};

export class PermissionEntity {
    private readonly name: string;
    private permissionData: PermissionAttributes;

    constructor(
        name: string | null,
        permissionData: PermissionAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.name = name!;
        this.permissionData = this.initialize(permissionData, customConfig);
    }

    private initialize(
        preData: PermissionAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): PermissionAttributes {
        this.validate(preData, customConfig);
        return {
            name: this.name,
            ...preData,
        } as PermissionAttributes;
    }

    private validate(preData: PermissionAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validatePermissionData(preData, customConfig);
        if (this.name) this.validateName(this.name);
    }

    private validateName(name: string): void {
        const schema = createConfig;
        const result = validated(schema, { name });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validatePermissionData(data: PermissionAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getName(): string {
        return this.name;
    }
    public getPermissionAttributes(): PermissionAttributes {
        return {
            id: this.permissionData.id,
            name: this.getName(),
            description: this.permissionData.description,
        } as PermissionsResponse;
    }

    public getPermissionRepresentation(): ClientRoleRepresentation {
        return {
            name: this.permissionData.name,
            description: this.permissionData.description,
        } as ClientRoleRepresentation;
    }

    public setAttributes(permissionData: PermissionAttributes, customConfig?: Record<string, FieldConfigType>): void {
        this.permissionData = this.initialize(permissionData, customConfig);
    }
}
