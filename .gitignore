# Node.js specific files
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json
.pnpm-debug.log*
.pnp.cjs
.pnp.data.json

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output/

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt/

# Bower dependency directory (https://bower.io/)
bower_components/

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Build artifacts
dist/
build/
tmp/
temp/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
pnpm-debug.log*
lerna-debug.log*

# Optional npm cache directory
.npm/

# Optional eslint cache
.eslintcache

# Next.js build output
.next/

# dotenv environment variables file
.env
.env.local
.env.*.local

# Vite cache
.vite/

# TypeScript cache
*.tsbuildinfo

# Editor directories and files
.idea/
.vscode/
*.sublime-workspace

# MacOS files
.DS_Store

# Node-Waffle test artifacts
artifacts/

db_sql

# agent
.claude
CLAUDE.md