import { v7 as uuidv7 } from "uuid";
import { PetSpeciesEnum } from "../../../enum/pet";
import { NutrientTypeEnum } from "../../../enum/requirement";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";

export const validUUID = uuidv7();
export const invalidUUID = "12kf46odfv3";

export const getOneMocks = {
    request: {
        validParams: {
            validUUID: validUUID,
        },
        invalidParams: {
            invalidUUID: invalidUUID,
        },
    },
    response: {
        validUUID: {
            id: validUUID,
            name: "Hand2010ReproducingGestation/Lactation",
            species: PetSpeciesEnum.DOG,
            nutrients: [
                {
                    nutrientName: "CrudeProtein",
                    unit: "%",
                    type: NutrientTypeEnum.ENERGY,
                    min: 10,
                    max: 20,
                },
            ],
            isDeleted: false,
            createdAt: new Date("2025-01-01T12:00:00Z"),
            updatedAt: new Date("2025-01-01T12:00:00Z"),
            deletedAt: null,
        },
    },
    error: {
        invalidUUID: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { id: v.UUID },
        },
        notFound: {
            errorType: e.REPOSITORY_ERROR,
            message: m.ID_NOT_FOUND,
            data: null,
            error: d.RECORD_NOT_FOUND,
        },
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ONE_REQUIREMENT_FAILED,
            data: null,
        },
    },
};

export const getListMocks = {
    request: {
        validParams: {
            nameAndSpecies: {
                species: PetSpeciesEnum.CAT,
                name: "AAFCO",
                limit: 2,
                page: 1,
            },
            speciesOnly: {
                species: PetSpeciesEnum.CAT,
                name: undefined,
                limit: 2,
                page: 1,
            },
            nameOnly: {
                species: undefined,
                name: "Hand2010",
                limit: 2,
                page: 1,
            },
        },
    },
    response: {
        nameAndSpecies: {
            rows: [
                {
                    id: uuidv7(),
                    name: "AAFCO2024AdultMaintenance",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
                {
                    id: uuidv7(),
                    name: "AAFCO2023AdultYoungRepro",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 15,
                            max: 999999,
                        },
                    ],
                },
            ],
            count: 4,
        },
        speciesOnly: {
            rows: [
                {
                    id: uuidv7(),
                    name: "AAFCO2024AdultMaintenance",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
                {
                    id: uuidv7(),
                    name: "AAFCO2023AdultYoungRepro",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 15,
                            max: 999999,
                        },
                    ],
                },
            ],
            count: 10,
        },
        nameOnly: {
            rows: [
                {
                    id: uuidv7(),
                    name: "Hand2010YoungAdult1-7Y(Obese)",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
                {
                    id: uuidv7(),
                    name: "Hand2010MatureAdult>7Y(NormalBCS)",
                    species: PetSpeciesEnum.DOG,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 15,
                            max: 999999,
                        },
                    ],
                },
            ],
            count: 10,
        },
        empty: {
            rows: [],
            count: 0,
        },
    },
    error: {
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_LIST_REQUIREMENT_WITH_FILTER_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};

export const getAllMocks = {
    request: null,
    response: {
        success: [
            {
                id: uuidv7(),
                name: "AAFCO2024AdultMaintenance",
                species: PetSpeciesEnum.DOG,
                nutrients: [
                    {
                        nutrientName: "CrudeProtein",
                        unit: "%",
                        type: NutrientTypeEnum.ENERGY,
                        min: 10,
                        max: 20,
                    },
                ],
                isDeleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                deletedAt: null,
            },
            {
                id: uuidv7(),
                name: "AAFCO2023AdultYoungRepro",
                species: PetSpeciesEnum.CAT,
                nutrients: [
                    {
                        nutrientName: "CrudeProtein",
                        unit: "%",
                        type: NutrientTypeEnum.ENERGY,
                        min: 15,
                        max: 999999,
                    },
                ],
                isDeleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                deletedAt: null,
            },
        ],
        empty: [],
    },
    error: {
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_REQUIREMENT_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};

export const getAllBySpeciesMocks = {
    request: {
        validParams: {
            validSpecies: PetSpeciesEnum.DOG,
        },
        invalidParams: {
            invalidSpecies: "bird" as unknown as PetSpeciesEnum,
        },
    },
    response: {
        validSpecies: [
            {
                name: "AAFCO2024AdultMaintenance",
            },
            {
                name: "AAFCO2023AdultYoungRepro",
            },
            {
                name: "Hand2010YoungAdult1-7Y(Obese)",
            },
            {
                name: "Hand2010MatureAdult>7Y(NormalBCS)",
            },
        ],
        empty: [],
    },
    error: {
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_REQUIREMENT_SPECIES_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};

export const createMultipleMocks = {
    request: {
        validParams: {
            validMultipleData: [
                {
                    name: "AAFCO2024AdultMaintenance",
                    species: PetSpeciesEnum.DOG,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
                {
                    name: "AAFCO2023AdultYoungRepro",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 15,
                            max: 999999,
                        },
                    ],
                },
            ],
        },
        invalidParams: {
            invalidMin: [
                {
                    name: "AAFCO2024AdultMaintenance",
                    species: PetSpeciesEnum.DOG,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: "10" as unknown as number,
                            max: 20,
                        },
                    ],
                },
                {
                    name: "AAFCO2023AdultYoungRepro",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 15,
                            max: 999999,
                        },
                    ],
                },
            ],
            invalidSpecies: [
                {
                    name: "AAFCO2024AdultMaintenance",
                    species: "bird" as unknown as PetSpeciesEnum,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
                {
                    name: "AAFCO2023AdultYoungRepro",
                    species: PetSpeciesEnum.CAT,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 15,
                            max: 999999,
                        },
                    ],
                },
            ],
        },
    },
    response: {
        validMultipleData: {
            insertedCount: 2,
        },
        empty: {
            insertedCount: 0,
        },
    },
    error: {
        invalidMin: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { nutrients: [{ min: v.NUMBER }] },
        },
        invalidSpecies: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { species: "InvalidEnum" },
        },
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_MULTIPLE_REQUIREMENTS_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};

export const createMocks = {
    request: {
        validParams: {
            validData: {
                name: "AAFCO2024AdultMaintenance",
                species: PetSpeciesEnum.DOG,
                nutrients: [
                    {
                        nutrientName: "CrudeProtein",
                        unit: "%",
                        type: NutrientTypeEnum.ENERGY,
                        min: 10,
                        max: 20,
                    },
                ],
            },
        },
        invalidParams: {
            invalidMin: {
                name: "AAFCO2024AdultMaintenance",
                species: PetSpeciesEnum.DOG,
                nutrients: [
                    {
                        nutrientName: "CrudeProtein",
                        unit: "%",
                        type: NutrientTypeEnum.ENERGY,
                        min: "10" as unknown as number,
                        max: 20,
                    },
                ],
            },
            invalidSpecies: {
                name: "AAFCO2024AdultMaintenance",
                species: "bird" as unknown as PetSpeciesEnum,
                nutrients: [
                    {
                        nutrientName: "CrudeProtein",
                        unit: "%",
                        type: NutrientTypeEnum.ENERGY,
                        min: 10,
                        max: 20,
                    },
                ],
            },
        },
    },
    response: {
        validData: {
            id: uuidv7(),
            name: "AAFCO2024AdultMaintenance",
            species: PetSpeciesEnum.DOG,
            nutrients: [
                {
                    nutrientName: "CrudeProtein",
                    unit: "%",
                    type: NutrientTypeEnum.ENERGY,
                    min: 10,
                    max: 20,
                },
            ],
            isDeleted: false,
            createdAt: new Date(),
            updatedAt: new Date(),
            deletedAt: null,
        },
    },
    error: {
        invalidMin: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { nutrients: [{ min: v.NUMBER }] },
        },
        invalidSpecies: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { species: "InvalidEnum" },
        },
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_REQUIREMENT_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};

export const updateMocks = {
    request: {
        validParams: {
            validData: {
                id: validUUID,
                data: {
                    name: "AAFCO2024AdultMaintenance",
                    species: PetSpeciesEnum.DOG,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
            },
        },
        invalidParams: {
            invalidUUIDData: {
                id: invalidUUID,
                data: {
                    name: "AAFCO2024AdultMaintenance",
                    species: PetSpeciesEnum.DOG,
                    nutrients: [
                        {
                            nutrientName: "CrudeProtein",
                            unit: "%",
                            type: NutrientTypeEnum.ENERGY,
                            min: 10,
                            max: 20,
                        },
                    ],
                },
            },
        },
    },
    response: {
        valid: true,
        invalid: false,
    },
    error: {
        invalidUUID: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { id: v.UUID },
        },
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.UPDATE_REQUIREMENT_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};

export const deleteMocks = {
    request: {
        validParams: {
            validUUID: validUUID,
        },
        invalidParams: {
            invalidUUID: invalidUUID,
        },
    },
    response: {
        validUUID: true,
        invalidUUIDData: false,
    },
    error: {
        invalidUUID: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: { id: v.UUID },
        },
        unexpectedModuleError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.DELETE_REQUIREMENT_FAILED,
            data: null,
        },
        unexpectRepositoryError: {
            errorType: e.REPOSITORY_ERROR,
            message: m.DATABASE_ERROR,
            error: d.DATABASE_TIMEOUT,
            data: null,
        },
    },
};
