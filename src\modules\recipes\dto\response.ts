import { RecipeIngredientEnum } from "../../../enum/recipe";
import { RecipeNutrient } from "../../../entities/recipes.entity";
import { NutrientTypeEnum } from "../../../enum/requirement";

export type RecipeResponse = {
    id?: string;
    name: string;
    ingredients: RecipeIngredientEnum[];
    nutrients: RecipeNutrient[];
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type RecipeListResponse = {
    rows: RecipeResponse[];
    count: number;
};

export type NutrientMatchResponse = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    value: number;
    min: number;
    max: number;
    isInRange: boolean;
}

export type RecipeMatchResponse = {
    recipeName: string;
    matchPercentage: number;
    recipeNutrients: NutrientMatchResponse[];
}

export type RecipesNutrientsMatchedResponse =  {
    matchingRecipes: RecipeMatchResponse[];
}
