import { TestCase } from "../../utils/testRunner";
import { UserModule } from "../../../modules/users/users.module";
import { UserRequest, UserFilterRequest } from "../../../modules/users/dto/request";
import { UserResponse, UserListResponse, CredentialsResponse } from "../../../modules/users/dto/response";
import {
    getAllMocks,
    getOneMocks,
    getListMocks,
    createMocks,
    updateMocks,
    deleteMocks,
    getRolesMocks,
    getPermissionsMocks,
    addPermissionMocks,
    removePermissionMocks,
} from "./users.test.mock";
import { DefaultError, ServiceError } from "../../../middlewares/errorHandler";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../../../enum/response";

type MethodTypeMapper = {
    getAll: { input: { token: string }; output: UserResponse[] };
    getOne: { input: { token: string; id: string }; output: UserResponse };
    getList: {
        input: { token: string; limit: number; page: number; filter: UserFilterRequest };
        output: UserListResponse;
    };
    create: { input: { token: string; data: UserRequest }; output: CredentialsResponse };
    update: { input: { token: string; id: string; data: UserRequest }; output: void };
    delete: { input: { token: string; id: string }; output: void };
    getRoles: { input: { token: string; id: string }; output: any };
    getPermissions: { input: { token: string; id: string }; output: any };
    addPermission: { input: { token: string; id: string; roleAttributes: any }; output: void };
    removePermission: { input: { token: string; id: string; roleAttributes: any }; output: void };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof UserModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

// Mock repository object
export const mockService = {
    getAllUsers: jest.fn(),
    getUserById: jest.fn(),
    searchUser: jest.fn(),
    createUser: jest.fn(),
    updateUser: jest.fn(),
    deleteUser: jest.fn(),
    generateUserCredential: jest.fn(),
    getUserGroups: jest.fn(),
    getClientRoleMappingsForUser: jest.fn(),
    addClientRoleToUser: jest.fn(),
    removeClientRoleFromUser: jest.fn(),
};
// Replace the real Repository or Service class with our mock version
jest.mock("../../../services/keycloak.service", () => ({
    KeycloakService: jest.fn(() => mockService),
}));

const moduleInstance = new UserModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Users",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all users",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getAllUsers.mockResolvedValue(getAllMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return all users in case of dto response",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getAllUsers.mockResolvedValue(getAllMocks.serviceResponse.successWithoutDto);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no users exist",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getAllUsers.mockResolvedValue(getAllMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getAllUsers.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getAllUsers.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error when token is invalid",
                    setup: {
                        input: {
                            token: getAllMocks.request.invalidToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getAllUsers.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error when token is forbidden",
                    setup: {
                        input: {
                            token: getAllMocks.request.forbiddenToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getAllUsers.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getAllUsers.mockResolvedValue(getAllMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getAllUsers.mockResolvedValue(getAllMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAllUsers,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getOne: {
            cases: [
                {
                    name: "[SUCCESS] should return one user",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserById.mockResolvedValue(getOneMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: getOneMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return one user in case of dto response",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserById.mockResolvedValue(getOneMocks.serviceResponse.successWithoutDto);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: getOneMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getUserById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getUserById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid uuid occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.invalidParams.id,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: getOneMocks.request.invalidToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getUserById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getOneMocks.request.forbiddenToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getUserById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when user not found",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.getUserById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserById.mockResolvedValue(getOneMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserById.mockResolvedValue(getOneMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return list of users",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.success,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of users in case of dto response",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.serviceResponse.successWithoutDto);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.success,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of users in case of pagination",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimitOne,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.successWithPagination,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no users exist",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.empty,
                            count: getListMocks.response.empty.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of users when filter is provided",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.validFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.success,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid filter is provided",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.invalidFilter,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidFilterError,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.searchUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.searchUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: getListMocks.request.invalidToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.searchUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getListMocks.request.forbiddenToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.searchUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                            filter: getListMocks.request.noneFilter,
                        },
                        mocks: () => {
                            mockService.searchUser.mockResolvedValue(getListMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.searchUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should create a user",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.generateUserCredential.mockReturnValue({
                                type: "password",
                                value: "mockedPassword",
                                temporary: true,
                            });
                            mockService.createUser.mockResolvedValue(createMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: createMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should create a user with roles",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBodyWithRoles,
                        },
                        mocks: () => {
                            mockService.generateUserCredential.mockReturnValue({
                                type: "password",
                                value: "mockedPassword",
                                temporary: true,
                            });
                            mockService.createUser.mockResolvedValue(createMocks.serviceResponse.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: createMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.createUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.createUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.missingBodyKey,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.invalidBodyValue,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: createMocks.request.invalidToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.createUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: createMocks.request.forbiddenToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.createUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when credentials invalid",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.generateUserCredential.mockReturnValue({});
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidCredentialsError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing credentials",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.generateUserCredential.mockReturnValue(undefined);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingCredentialsError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        update: {
            cases: [
                {
                    name: "[SUCCESS] should update a user",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.updateUser.mockResolvedValue(updateMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should update a user with roles",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBodyWithRole,
                        },
                        mocks: () => {
                            mockService.updateUser.mockResolvedValue(updateMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.updateUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.updateUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: updateMocks.request.invalidToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.updateUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: updateMocks.request.forbiddenToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.updateUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator UUID occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.invalidParams.id,
                            data: updateMocks.request.validBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.invalidBodyValue,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator id and data occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.invalidParams.id,
                            data: updateMocks.request.invalidBodyValue,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidAllError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when user not found",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.updateUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        delete: {
            cases: [
                {
                    name: "[SUCCESS] should delete a user",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.deleteUser.mockResolvedValue(deleteMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.deleteUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.deleteUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: deleteMocks.request.invalidToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.deleteUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: deleteMocks.request.forbiddenToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.deleteUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid uuid occurs",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.invalidParams.id,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when user not found",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.deleteUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getRoles: {
            cases: [
                {
                    name: "[SUCCESS] should return all roles of a user",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserGroups.mockResolvedValue(getRolesMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: getRolesMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty array when user has no roles",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserGroups.mockResolvedValue(getRolesMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: getRolesMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid uuid occurs",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.invalidParams.id,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getUserGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getUserGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: getRolesMocks.request.invalidToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getUserGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for unauthorized user",
                    setup: {
                        input: {
                            token: getRolesMocks.request.forbiddenToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getUserGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when user not found",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.getUserGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator role occurs",
                    setup: {
                        input: {
                            token: getRolesMocks.request.validToken,
                            id: getRolesMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getUserGroups.mockResolvedValue(getRolesMocks.response.invalidRoleValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getRoles(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getRolesMocks.error.invalidRoleError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getUserGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getPermissions: {
            cases: [
                {
                    name: "[SUCCESS] should return all permissions of a user",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getClientRoleMappingsForUser.mockResolvedValue(
                                getPermissionsMocks.response.success
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: getPermissionsMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when user has no permissions",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getClientRoleMappingsForUser.mockResolvedValue(
                                getPermissionsMocks.response.empty
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: getPermissionsMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid uuid occurs",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.invalidParams.id,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getClientRoleMappingsForUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.invalidToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getClientRoleMappingsForUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for unauthorized user",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.forbiddenToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getClientRoleMappingsForUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when user not found",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.getClientRoleMappingsForUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getClientRoleMappingsForUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid permission occurs",
                    setup: {
                        input: {
                            token: getPermissionsMocks.request.validToken,
                            id: getPermissionsMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getClientRoleMappingsForUser.mockResolvedValue(
                                getPermissionsMocks.response.invalidPermissionValue
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getPermissions(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getPermissionsMocks.error.invalidPermissionError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getClientRoleMappingsForUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        addPermission: {
            cases: [
                {
                    name: "[SUCCESS] should add a permission to a user",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.addClientRoleToUser.mockResolvedValue(addPermissionMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.addClientRoleToUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.invalidToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.addClientRoleToUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for unauthorized user",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.forbiddenToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.addClientRoleToUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error for invalid user id",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.invalidParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error for invalid permission occurs",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.invalidBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.invalidPermissionError,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.validParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.addClientRoleToUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when user not found",
                    setup: {
                        input: {
                            token: addPermissionMocks.request.validToken,
                            id: addPermissionMocks.request.notFoundParams.id,
                            roleAttributes: addPermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.addClientRoleToUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.addPermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: addPermissionMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.addClientRoleToUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        removePermission: {
            cases: [
                {
                    name: "[SUCCESS] should remove permission from user",
                    setup: {
                        input: {
                            token: removePermissionMocks.request.validToken,
                            id: removePermissionMocks.request.validParams.id,
                            roleAttributes: removePermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.removeClientRoleFromUser.mockResolvedValue(
                                removePermissionMocks.response.success.data
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.removePermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.removeClientRoleFromUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error for invalid uuid",
                    setup: {
                        input: {
                            token: removePermissionMocks.request.validToken,
                            id: removePermissionMocks.request.invalidParams.id,
                            roleAttributes: removePermissionMocks.request.validBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.removePermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: removePermissionMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.removeClientRoleFromUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error for invalid permission",
                    setup: {
                        input: {
                            token: removePermissionMocks.request.validToken,
                            id: removePermissionMocks.request.validParams.id,
                            roleAttributes: removePermissionMocks.request.invalidBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.removePermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: removePermissionMocks.error.invalidPermissionError,
                        },
                        methodCalls: [
                            {
                                method: mockService.removeClientRoleFromUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when initialization fails",
                    setup: {
                        input: {
                            token: removePermissionMocks.request.validToken,
                            id: removePermissionMocks.request.validParams.id,
                            roleAttributes: removePermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.removePermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: removePermissionMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.removeClientRoleFromUser,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when unavailable occurs",
                    setup: {
                        input: {
                            token: removePermissionMocks.request.validToken,
                            id: removePermissionMocks.request.validParams.id,
                            roleAttributes: removePermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.removeClientRoleFromUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.removePermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: removePermissionMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.removeClientRoleFromUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: removePermissionMocks.request.validToken,
                            id: removePermissionMocks.request.validParams.id,
                            roleAttributes: removePermissionMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.removeClientRoleFromUser.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.removePermission(input.token, input.id, input.roleAttributes);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: removePermissionMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.removeClientRoleFromUser,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
