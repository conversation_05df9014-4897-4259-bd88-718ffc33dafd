import { TestCase } from "../../utils/testRunner";
import { DoctorModule } from "../../../modules/doctors/doctors.module";
import { DoctorRequest } from "../../../modules/doctors/dto/request";
import { getAllMocks, createMocks, updateMocks, deleteMocks } from "./doctors.test.mock";
import { DefaultError, QueryError } from "../../../middlewares/errorHandler";
import { DoctorAttributes } from "../../../entities/doctors.entity";
import { DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m } from "../../../enum/response";

type MethodTypeMapper = {
    create: { input: { data: DoctorRequest }; output: void };
    getAll: { input: void; output: DoctorAttributes[] };
    update: { input: { id: string; data: DoctorRequest }; output: void };
    delete: { input: { id: string }; output: void };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof DoctorModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

export const mockService = {
    getAll: jest.fn(),
    create: jest.fn(),
    findDoctor: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
};

jest.mock("../../../repositories/doctors.repository", () => ({
    DoctorRepository: jest.fn(() => mockService),
}));

const moduleInstance = new DoctorModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Doctors",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all doctors",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.success.data);
                        },
                    },
                    method: async (input: void) => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty array when no doctors found",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input: void) => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.empty.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when unexpected error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input: void) => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when missing data error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.missingKey.data);
                        },
                    },
                    method: async (input: void) => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when missing info key error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.missingInfoKey.data);
                        },
                    },
                    method: async (input: void) => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingInfoKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when invalid value error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.invalidValue.data);
                        },
                    },
                    method: async (input: void) => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should create a new doctor",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(createMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: createMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should create a new doctor without email",
                    setup: {
                        input: {
                            data: createMocks.request.validBodyWithoutEmail,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(createMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: createMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing required fields",
                    setup: {
                        input: {
                            data: createMocks.request.missingBodyKey as unknown as DoctorRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.create,
                                called: false,
                            },
                            {
                                method: mockService.findDoctor,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid value",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyValue,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.create,
                                called: false,
                            },
                            {
                                method: mockService.findDoctor,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw default error when unexpected error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(createMocks.serviceResponse.empty.data);
                            mockService.create.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate name error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockImplementationOnce(() => createMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(() => createMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(
                                () => createMocks.serviceResponse.duplicateName.data
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.duplicateName,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate phone number error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockImplementationOnce(
                                () => createMocks.serviceResponse.duplicatePhoneNumber.data
                            );
                            mockService.findDoctor.mockImplementationOnce(() => createMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(() => createMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.duplicatePhoneNumber,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate email error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockImplementationOnce(() => createMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(
                                () => createMocks.serviceResponse.duplicateEmail.data
                            );
                            mockService.findDoctor.mockImplementationOnce(() => createMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.duplicateEmail,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },

                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate multiple data error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(createMocks.serviceResponse.duplicateData.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.duplicateData,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        update: {
            cases: [
                {
                    name: "[SUCCESS] should update a doctor",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(updateMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: updateMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should update a doctor without email",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBodyWithoutEmail,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(updateMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: updateMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid id",
                    setup: {
                        input: {
                            id: updateMocks.request.invalidId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidId,
                        },
                        methodCalls: [
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing required fields",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.missingBodyRequiredKey,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid value",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.invalidBodyValue,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidBodyValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate phone number error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockImplementationOnce(
                                () => updateMocks.serviceResponse.duplicatePhoneNumber.data
                            );
                            mockService.findDoctor.mockImplementationOnce(() => updateMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(() => updateMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.duplicatePhoneNumber,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate email error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockImplementationOnce(() => updateMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(
                                () => updateMocks.serviceResponse.duplicateEmail.data
                            );
                            mockService.findDoctor.mockImplementationOnce(() => updateMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.duplicateEmail,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate name error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockImplementationOnce(() => updateMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(() => updateMocks.serviceResponse.empty.data);
                            mockService.findDoctor.mockImplementationOnce(
                                () => updateMocks.serviceResponse.duplicateName.data
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.duplicateName,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate multiple data error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(updateMocks.serviceResponse.duplicateData.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.duplicateData,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw default error when unexpected error occurs",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(updateMocks.serviceResponse.empty.data);
                            mockService.update.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw database error when doctor not found",
                    setup: {
                        input: {
                            id: updateMocks.request.validId.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findDoctor.mockResolvedValue(updateMocks.serviceResponse.empty.data);
                            mockService.update.mockRejectedValue(new QueryError(m.NOT_FOUND, d.RECORD_NOT_FOUND));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.findDoctor,
                                called: true,
                            },
                            {
                                method: mockService.update,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        delete: {
            cases: [
                {
                    name: "[SUCCESS] should delete a doctor",
                    setup: {
                        input: {
                            id: deleteMocks.request.validId.id,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: deleteMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid id",
                    setup: {
                        input: {
                            id: deleteMocks.request.invalidId.id,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidId,
                        },
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when unexpected error occurs",
                    setup: {
                        input: {
                            id: deleteMocks.request.validId.id,
                        },
                        mocks: () => {
                            mockService.delete.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw database error when doctor not found",
                    setup: {
                        input: {
                            id: deleteMocks.request.validId.id,
                        },
                        mocks: () => {
                            mockService.delete.mockRejectedValue(new QueryError(m.NOT_FOUND, d.RECORD_NOT_FOUND));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.delete,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
