import { NutrientValidateType } from "../nutrients/nutrients.type";
export interface RawmatCreateValidateType {
    name: string;
    nutrients: NutrientValidateType[];
    type: string;
    costPerGram: number;
    mePerGram: number;
}
export interface RawmatReceiveValidateType {
    rawMatId: string;
    name: string;
    nutrients: NutrientValidateType[];
    type: string;
    costPerGram: number;
    mePerGram: number;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
    deletedAt: Date | null;
}
