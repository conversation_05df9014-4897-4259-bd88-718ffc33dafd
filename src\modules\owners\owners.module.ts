import { OwnerAttributes, OwnerEntity } from "../../entities/owners.entity";
import {
    FormatValidationError,
    DefaultError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { OwnerRequest, GetOwnerListFilterRequest, GetAllOwnerSearchRequest } from "./dto/request";
import { OwnerListResponse, OwnerResponse, OwnerResolveError } from "./dto/response";
import { OwnerRepository } from "../../repositories/owners.repository";
import { getAllFilterConfig } from "../../validations/owners/owners.validate.config";

export class OwnerModule {
    private readonly ownerRepository;
    constructor() {
        this.ownerRepository = new OwnerRepository();
    }
    public async getOne(id: string): Promise<OwnerResponse | null> {
        try {
            const ownerEntity = new OwnerEntity(id);
            const validId = ownerEntity.getId();

            const response = await this.ownerRepository.findOwnerById(validId);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_OWNER_FAILED);
        }
    }

    public async getAll(filter?: GetAllOwnerSearchRequest): Promise<OwnerResponse[]> {
        try {
            const ownerEntity = new OwnerEntity(null, null);
            if (filter) ownerEntity.validateFilter(filter, getAllFilterConfig);

            const response = await this.ownerRepository.findAll(filter);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_OWNER_FAILED);
        }
    }

    public async getList(limit: number, page: number, filter?: GetOwnerListFilterRequest): Promise<OwnerListResponse> {
        try {
            const ownerEntity = new OwnerEntity(null, null);
            if (filter) ownerEntity.validateFilter(filter);

            const response = await this.ownerRepository.findList(limit, page, filter);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_OWNER_FAILED);
        }
    }

    public async create(data: OwnerRequest): Promise<OwnerResponse> {
        try {
            const errorRes: OwnerResolveError = {
                ownerInfo: {},
            };
            const ownerData = data as OwnerAttributes;
            const ownerEntity = new OwnerEntity(null, ownerData);
            const validOwner = ownerEntity.getAttributes();

            const existingOwner = await this.ownerRepository.findByPhoneNumber(validOwner.ownerInfo.phoneNumber);
            
            if (existingOwner) errorRes.ownerInfo = { phoneNumber: d.DUPLICATED_PHONE };
            if (Object.keys(errorRes.ownerInfo).length > 0) {
                throw new DefaultError(e.DATABASE_ERROR, m.DUPLICATED_DATA, null, errorRes, s.CONFLICT);
            }

            const response = await this.ownerRepository.create(validOwner);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_OWNER_FAILED);
        }
    }

    public async update(id: string, data: OwnerRequest): Promise<boolean> {
        try {
            const errorRes: OwnerResolveError = {
                ownerInfo: {},
            };
            const ownerData = data as OwnerAttributes;
            const ownerEntity = new OwnerEntity(id, ownerData);
            const validId = ownerEntity.getId();
            const validData = ownerEntity.getAttributes();

            const existingOwner = await this.ownerRepository.findByPhoneNumber(validData.ownerInfo.phoneNumber);
            
            if (existingOwner) errorRes.ownerInfo = { phoneNumber: d.DUPLICATED_PHONE };
            
            if (Object.keys(errorRes.ownerInfo).length > 0) {
                throw new DefaultError(e.DATABASE_ERROR, m.DUPLICATED_DATA, null, errorRes, s.CONFLICT);
            }

            const response = await this.ownerRepository.update(validId, validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_OWNER_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const ownerEntity = new OwnerEntity(id);
            const validId = ownerEntity.getId();

            const response = await this.ownerRepository.delete(validId);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_OWNER_FAILED);
        }
    }
}
