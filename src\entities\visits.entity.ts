import { v7 as uuidv7 } from "uuid";
import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { FieldConfigType } from "../types/validate.type";
import { createConfig, filterConfig } from "../validations/visits/visits.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { VisitStatusEnum, VisitStepEnum, MappingAlgorithmEnum, PetAllergicEnum } from "../enum/visit";
import { PetInfo, Diseases } from "./pets.entity";
import { MatchingRecipes } from "./orders.entity";
import { NutrientDetailRange } from "./requirements.entity";
import { Questionaires } from "./questionaires.entity";
import { DoctorInfo } from "./doctors.entity";
import { VisitRequest, GetVisitListFilterRequest } from "../modules/visits/dto/request";

// Pet Health Submit
export type PetHealthSubmitType = {
    currentBcs: number;
    idealBcs: number;
    currentWeight: number;
    idealWeight: number;
    questionaires: Questionaires[];
};

// Pet Requirement Submit
export type RequirementsInfo = {
    mappingMethod: MappingAlgorithmEnum;
    profileRef: string;
    requirements: string[];
};

export type ExtraMealDetails = {
    foodName: string;
    brand: string;
    dailyAmount: number;
    compositions: {
        protein: number | null;
        fat: number | null;
        fiber: number | null;
        moisture: number | null;
        sodium: number | null;
        ash: number | null;
        me: number | null;
    } | null;
};

export type FoodsHistory = {
    extraMeals: ExtraMealDetails[];
    allergic: PetAllergicEnum[];
    foodsToAvoid: PetAllergicEnum[];
};

export type PetRequirementSubmitType = {
    diseases: Diseases[];
    requirementsInfo: RequirementsInfo;
    nutritionNeeds: string;
    foodsHistory: FoodsHistory;
};

// Pet Nutrition Submit
export type EnergyRequirements = {
    stomachSize: number;
    weightTypeSelected: string;
    factor: number;
    rer: number;
    der: number;
};

export type PetNutritionSubmitType = {
    personalNutrients: NutrientDetailRange[];
    energyRequirements: EnergyRequirements;
};

// Pet Product Match Submit
export type PetProductMatchSubmitType = {
    matchingRecipes: MatchingRecipes[];
};

// Visit Attributes
export type VisitAttributes = {
    id: string;
    petId: string;
    doctorId: string;
    petInfo: PetInfo;
    doctorInfo: DoctorInfo;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    petHealth: PetHealthSubmitType | null;
    petRequirement: PetRequirementSubmitType | null;
    petNutrition: PetNutritionSubmitType | null;
    petProductMatch: PetProductMatchSubmitType | null;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type VisitInputData = VisitRequest | VisitAttributes | null;
export class VisitEntity {
    private readonly id: string | null;
    private readonly visitData: VisitAttributes;
    constructor(
        id: string | null = null,
        preData: VisitInputData = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.visitData = this.initialize(preData, customConfig);
    }

    private initialize(preData: VisitInputData, customConfig?: Record<string, FieldConfigType>): VisitAttributes {
        try {
            const validatedData = this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                petId: validatedData?.petId,
                doctorId: validatedData?.doctorId,
                petInfo: validatedData?.petInfo,
                doctorInfo: validatedData?.doctorInfo,
                visitStep: validatedData?.visitStep,
                status: validatedData?.status,
                petHealth: validatedData?.petHealth,
                petRequirement: validatedData?.petRequirement,
                petNutrition: validatedData?.petNutrition,
                petProductMatch: validatedData?.petProductMatch,
            } as VisitAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: VisitInputData, customConfig?: Record<string, FieldConfigType>): VisitAttributes | null {
        let validatedData: VisitAttributes | null = null;
        if (preData) validatedData = this.validateVisitData(preData, customConfig);
        if (this.id) this.validateId(this.id);
        return validatedData;
    }

    private validateVisitData(
        data: VisitAttributes | VisitRequest,
        customConfig?: Record<string, FieldConfigType>
    ): VisitAttributes {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
        return result.data as VisitAttributes;
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public validateFilter(filter: GetVisitListFilterRequest): void {
        const result = validated(filterConfig, filter);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.visitData.id;
    }

    public getAttributes(): VisitAttributes {
        return this.visitData;
    }
}
