import { DoctorRepository } from "../../repositories/doctors.repository";
import { FormatValidationError, DefaultError, QueryError } from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { DoctorRequest } from "./dto/request";
import { DoctorDuplicateError } from "./dto/response";
import { DoctorEnti<PERSON>, DoctorAttributes } from "../../entities/doctors.entity";
export class DoctorModule {
    private readonly doctorRepository;
    constructor() {
        this.doctorRepository = new DoctorRepository();
    }
    public async getAll(): Promise<DoctorAttributes[]> {
        try {
            const doctors = await this.doctorRepository.getAll();
            const response = doctors.map((doctor) => {
                const doctorEntity = new DoctorEntity(doctor.id, doctor);
                return doctorEntity.getAttributes();
            });
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_DOCTORS_FAILED);
        }
    }
    public async create(data: DoctorRequest): Promise<void> {
        try {
            const doctorEntity = new DoctorEntity(null, null);
            doctorEntity.setDoctorInfo({
                prefix: data.prefix,
                firstName: data.firstName,
                lastName: data.lastName,
                phoneNumber: data.phoneNumber,
                email: data.email ?? null,
            });
            const doctorAttributes = doctorEntity.getAttributes();
            const { phoneNumber, email, firstName, lastName } = doctorAttributes.doctorInfo;
            const errorRes: DoctorDuplicateError = {};
            const [existingByPhoneNumber, existingByEmail, existingByName] = await Promise.all([
                this.doctorRepository.findDoctor({ phoneNumber }),
                data.email ? this.doctorRepository.findDoctor({ email }) : Promise.resolve(null),
                this.doctorRepository.findDoctor({
                    firstName,
                    lastName,
                }),
            ]);

            if (existingByPhoneNumber) errorRes.phoneNumber = m.DUPLICATED_PHONE_NUMBER;
            if (existingByEmail) errorRes.email = m.DUPLICATED_EMAIL;
            if (existingByName) errorRes.name = m.DUPLICATED_NAME;
            if (Object.keys(errorRes).length > 0)
                throw new DefaultError(e.DATABASE_ERROR, m.DUPLICATED_DATA, null, errorRes, s.CONFLICT);

            await this.doctorRepository.create(doctorAttributes);
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_DOCTOR_FAILED);
        }
    }
    public async update(id: string, data: DoctorRequest): Promise<void> {
        try {
            const doctorEntity = new DoctorEntity(id, null);
            doctorEntity.setDoctorInfo({
                prefix: data.prefix,
                firstName: data.firstName,
                lastName: data.lastName,
                phoneNumber: data.phoneNumber,
                email: data.email ?? null,
            });
            const doctorInfo = doctorEntity.getDoctorInfo();
            const { phoneNumber, email, firstName, lastName } = doctorInfo;
            const errorRes: DoctorDuplicateError = {};
            const [existingByPhoneNumber, existingByEmail, existingByName] = await Promise.all([
                this.doctorRepository.findDoctor({ phoneNumber }),
                data.email ? this.doctorRepository.findDoctor({ email }) : Promise.resolve(null),
                this.doctorRepository.findDoctor({
                    firstName,
                    lastName,
                }),
            ]);
            const doctorId = doctorEntity.getId();
            if (existingByPhoneNumber && existingByPhoneNumber.id !== doctorId)
                errorRes.phoneNumber = m.DUPLICATED_PHONE_NUMBER;
            if (existingByEmail && existingByEmail.id !== doctorId) errorRes.email = m.DUPLICATED_EMAIL;
            if (existingByName && existingByName.id !== doctorId) errorRes.name = m.DUPLICATED_NAME;
            if (Object.keys(errorRes).length > 0)
                throw new DefaultError(e.DATABASE_ERROR, m.DUPLICATED_DATA, null, errorRes, s.CONFLICT);
            await this.doctorRepository.update(doctorId, { doctorInfo });
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            if (error instanceof QueryError) {
                throw new DefaultError(e.DATABASE_ERROR, error.message, null, error.errors, s.NOT_FOUND);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_DOCTOR_FAILED);
        }
    }
    public async delete(id: string): Promise<void> {
        try {
            const validateId = new DoctorEntity(id, null);
            const validId = validateId.getId();
            await this.doctorRepository.delete(validId);
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof QueryError) {
                throw new DefaultError(e.DATABASE_ERROR, error.message, null, error.errors, s.NOT_FOUND);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_DOCTOR_FAILED);
        }
    }
}
