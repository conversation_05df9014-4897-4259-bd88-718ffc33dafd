import { RecipeAttributes, RecipeEntity } from "../../entities/recipes.entity";
import {
    ModuleError,
    FormatValidationError,
    DefaultError,
    EntityError,
    ServiceError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import { RecipeRequest, RecipeMatchRequest, NutrientMatchRequest } from "./dto/request";
import {
    RecipeResponse,
    RecipeListResponse,
    NutrientMatchResponse,
    RecipeMatchResponse,
    RecipesNutrientsMatchedResponse,
} from "./dto/response";
import { RecipeRepository } from "../../repositories/recipes.repository";
import { RecipeIngredientEnum } from "../../enum/recipe";
import { PetAllergicEnum } from "../../enum/visit";
import { recipeMatchingConstants } from "../../constants/recipe";

interface PetRequirementDetails {
    allergic: PetAllergicEnum[];
    foodsToAvoid: PetAllergicEnum[];
}

export class RecipeModule {
    private readonly recipeRepository;
    constructor() {
        this.recipeRepository = new RecipeRepository();
    }

    public async findMatchingRecipes(data: RecipeMatchRequest): Promise<RecipesNutrientsMatchedResponse> {
        try {
            const { petNutrition, petRequirement } = data;

            const recipes = await this.recipeRepository.findAll();

            // Combine allergic and foods to avoid into a single array and filter recipes
            const restrictedIngredients = this.getRestrictedIngredients(petRequirement);
            const eligibleRecipes = this.filterEligibleRecipes(recipes, restrictedIngredients);

            if (eligibleRecipes.length === 0) {
                return { matchingRecipes: [] };
            }

            const targetNutrientMap = new Map(
                petNutrition.nutrients.map((nutrient) => [nutrient.nutrientName, nutrient])
            );

            // Compare each recipe nutrient value is in range of request nutrient min/max range or not
            const recipeMatches = await this.matchRecipesToNutrients(eligibleRecipes, targetNutrientMap);

            const sortedMatches = [...recipeMatches]
                .filter((match) => match.matchPercentage > 0)
                .sort((a, b) => b.matchPercentage - a.matchPercentage);
            const topMatches = sortedMatches.slice(0, recipeMatchingConstants.TOP_MATCHES);

            return {
                matchingRecipes: topMatches,
            };
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.MATCHING_RECIPES_FAILED);
        }
    }

    public async getOne(id: string): Promise<RecipeResponse> {
        try {
            const recipeEntity = new RecipeEntity(id);
            const validId = recipeEntity.getId();
            const response = await this.recipeRepository.findRecipeById(validId);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_RECIPE_FAILED);
        }
    }

    public async getList(limit: number, page: number): Promise<RecipeListResponse> {
        try {
            const response = await this.recipeRepository.findList(limit, page);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_RECIPE_FAILED);
        }
    }

    public async getAll(): Promise<RecipeResponse[]> {
        try {
            const response = await this.recipeRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_RECIPE_FAILED);
        }
    }

    public async createMultiple(data: RecipeRequest[]): Promise<{ insertedCount: number }> {
        try {
            const validatedRecipes = data.map((recipeRequest) => {
                const recipeData = recipeRequest as RecipeAttributes;
                const recipeEntity = new RecipeEntity(null, recipeData);
                return recipeEntity.getAttributes();
            });

            const response = await this.recipeRepository.createRecipes(validatedRecipes);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_MULTIPLE_RECIPES_FAILED);
        }
    }

    public async create(data: RecipeRequest): Promise<RecipeResponse> {
        try {
            const recipeData = data as RecipeAttributes;
            const recipeEntity = new RecipeEntity(null, recipeData);
            const validData = recipeEntity.getAttributes();

            const existingRecipe = await this.recipeRepository.findExistingRecipeByName(validData.name);

            if (existingRecipe) throw new ModuleError(m.DUPLICATED_RECIPE_NAME);

            const response = await this.recipeRepository.create(validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_RECIPE_FAILED);
        }
    }

    public async update(id: string, data: RecipeRequest): Promise<boolean> {
        try {
            const recipeData = data as RecipeAttributes;
            const recipeEntity = new RecipeEntity(id, recipeData);
            const validId = recipeEntity.getId();
            const validData = recipeEntity.getAttributes();

            const response = await this.recipeRepository.update(validId, validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_RECIPE_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const validateId = new RecipeEntity(id);
            const validId = validateId.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.recipeRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_RECIPE_FAILED);
        }
    }

    private getRestrictedIngredients(petRequirement: PetRequirementDetails): (RecipeIngredientEnum | PetAllergicEnum)[] {
        const restrictedIngredients: (RecipeIngredientEnum | PetAllergicEnum)[] = [];

        if (petRequirement.allergic && petRequirement.allergic.length > 0) {
            restrictedIngredients.push(...petRequirement.allergic);
        }
        if (petRequirement.foodsToAvoid && petRequirement.foodsToAvoid.length > 0) {
            restrictedIngredients.push(...petRequirement.foodsToAvoid);
        }

        return restrictedIngredients;
    }

    private filterEligibleRecipes(
        recipes: RecipeResponse[],
        restrictedIngredients: (RecipeIngredientEnum | PetAllergicEnum)[]
    ): RecipeResponse[] {
        return restrictedIngredients.length > 0
            ? recipes.filter(
                  (recipe) => !recipe.ingredients.some((ingredient) => restrictedIngredients.includes(ingredient))
              )
            : recipes;
    }

    private async matchRecipesToNutrients(
        eligibleRecipes: RecipeResponse[],
        targetNutrientMap: Map<string, NutrientMatchRequest>
    ): Promise<RecipeMatchResponse[]> {
        return Promise.all(
            eligibleRecipes.map(async (recipe) => {
                const nutrientMatches: NutrientMatchResponse[] = [];
                let matchingCount = 0;

                for (const targetNutrientName of targetNutrientMap.keys()) {
                    const targetNutrient = targetNutrientMap.get(targetNutrientName)!;
                    const recipeNutrient = recipe.nutrients.find((n) => n.nutrientName === targetNutrientName);

                    const isInRange = recipeNutrient
                        ? recipeNutrient.value >= targetNutrient.min && recipeNutrient.value <= targetNutrient.max
                        : false;

                    if (isInRange) matchingCount++;

                    nutrientMatches.push({
                        nutrientName: targetNutrientName,
                        unit: targetNutrient.unit,
                        type: targetNutrient.type,
                        value: recipeNutrient?.value ?? 0,
                        min: targetNutrient.min,
                        max: targetNutrient.max,
                        isInRange,
                    });
                }

                const matchPercentage = (matchingCount / targetNutrientMap.size) * 100;

                return {
                    recipeName: recipe.name,
                    matchPercentage: Number(matchPercentage.toFixed(recipeMatchingConstants.DECIMAL_PLACES)),
                    recipeNutrients: nutrientMatches,
                };
            })
        );
    }
}
