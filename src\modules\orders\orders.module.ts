import { OrderEntity, OrderAttributes } from "../../entities/orders.entity";
import {
    FormatValidationError,
    DefaultError,
    EntityError,
    ServiceError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import { OrderRequest } from "./dto/request";
import { OrderListResponse, OrderResponse } from "./dto/response";
import { OrderRepository } from "../../repositories/orders.repository";

export class OrderModule {
    private readonly orderRepository;
    constructor() {
        this.orderRepository = new OrderRepository();
    }

    public async getOne(id: string): Promise<OrderResponse | null> {
        try {
            const orderEntity = new OrderEntity(id);
            const validId = orderEntity.getId();
            const response = await this.orderRepository.findOrderById(validId);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_ORDER_FAILED);
        }
    }

    public async getAll(): Promise<OrderResponse[]> {
        try {
            const response = await this.orderRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_ORDER_FAILED);
        }
    }

    public async getList(limit: number, page: number): Promise<OrderListResponse> {
        try {
            const response = await this.orderRepository.findList(limit, page);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_ORDER_FAILED);
        }
    }

    public async create(data: OrderRequest): Promise<OrderResponse> {
        try {
            const orderData = data as OrderAttributes;
            const orderEntity = new OrderEntity(null, orderData);
            const validData = orderEntity.getAttributes();
            const response = await this.orderRepository.create(validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_ORDER_FAILED);
        }
    }

    public async update(id: string, data: OrderRequest): Promise<boolean> {
        try {
            const orderData = data as OrderAttributes;
            const orderEntity = new OrderEntity(id, orderData);
            const validId = orderEntity.getId();
            const validData = orderEntity.getAttributes();
            const response = await this.orderRepository.update(validId, validData);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_ORDER_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const orderEntity = new OrderEntity(id);
            const validId = orderEntity.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.orderRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_ORDER_FAILED);
        }
    }
}
