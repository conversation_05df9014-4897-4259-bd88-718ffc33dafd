import { RawmatRepository } from "../../repositories/rawmats.repository";
import { FormatValidationError, DefaultError } from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { RawmatEntity } from "../../entities/rawmats.entity";
import { RawmatRequest } from "./dto/request";
import { RawmatResponse } from "./dto/response";

export class RawmatModule {
    private readonly rawmatRepository;
    constructor() {
        this.rawmatRepository = new RawmatRepository();
    }
    public async getAll(): Promise<RawmatResponse[]> {
        try {
            const rawmats = await this.rawmatRepository.getAll();
            const response = rawmats.map((rawmat) => {
                const rawmatEntity = new RawmatEntity(rawmat.rawMatId, rawmat);
                return rawmatEntity.getResponse();
            });
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_RAWMATS_FAILED);
        }
    }
    public async create(data: RawmatRequest): Promise<void> {
        try {
            const rawmatEntity = new RawmatEntity(null, null);
            rawmatEntity.setRawmat(data);
            const rawmatAttributes = rawmatEntity.getAttributes();

            const existingByName = await this.rawmatRepository.findByName(rawmatAttributes.name);
            if (existingByName)
                throw new DefaultError(
                    e.DATABASE_ERROR,
                    m.DUPLICATED_DATA,
                    null,
                    { name: m.DUPLICATED_NAME },
                    s.CONFLICT
                );

            await this.rawmatRepository.create(rawmatAttributes);
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof DefaultError) {
                throw error;
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_RAWMAT_FAILED);
        }
    }
}
