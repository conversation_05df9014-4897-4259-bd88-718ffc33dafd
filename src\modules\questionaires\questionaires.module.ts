import { QuestionaireAttributes, QuestionaireEntity } from "../../entities/questionaires.entity";
import {
    ModuleError,
    FormatValidationError,
    DefaultError,
    EntityError,
    ServiceError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import { QuestionaireRequest } from "./dto/request";
import { QuestionaireResponse } from "./dto/response";
import { QuestionaireRepository } from "../../repositories/questionaires.repository";
import { PetSpeciesEnum } from "../../enum/pet";

export class QuestionaireModule {
    private readonly questionaireRepository;
    constructor() {
        this.questionaireRepository = new QuestionaireRepository();
    }
    public async getOneById(id: string): Promise<QuestionaireResponse> {
        try {
            const questionareEntity = new QuestionaireEntity(id);
            const validId = questionareEntity.getId();
            const response = await this.questionaireRepository.findOneById(validId);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_QUESTIONAIRE_FAILED);
        }
    }

    public async getOneBySpeciesAndType(species: PetSpeciesEnum, type: string): Promise<QuestionaireResponse> {
        try {
            const response = await this.questionaireRepository.findOneBySpeciesAndType(species, type);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_QUESTIONAIRE_FAILED);
        }
    }

    public async getAll(): Promise<QuestionaireResponse[]> {
        try {
            const response = await this.questionaireRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_QUESTIONAIRE_FAILED);
        }
    }

    public async create(data: QuestionaireRequest): Promise<QuestionaireResponse> {
        try {
            const questionaireData = data as QuestionaireAttributes;
            const questionaireEntity = new QuestionaireEntity(null, questionaireData);
            const validData = questionaireEntity.getAttributes();

            const existingQuestionaire = await this.questionaireRepository.findExistingBySpeciesAndType(
                validData.species,
                validData.type
            );
            if (existingQuestionaire) throw new ModuleError(m.DUPLICATED_SPECIES_TYPE);

            const response = await this.questionaireRepository.create(validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ModuleError) {
                throw new DefaultError(e.MODULE_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_QUESTIONAIRE_FAILED);
        }
    }

    public async update(id: string, data: QuestionaireRequest): Promise<boolean> {
        try {
            const questionaireData = data as QuestionaireAttributes;
            const questionaireEntity = new QuestionaireEntity(id, questionaireData);
            const validId = questionaireEntity.getId();
            const validData = questionaireEntity.getAttributes();

            const response = await this.questionaireRepository.update(validId, validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_QUESTIONAIRE_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const validateId = new QuestionaireEntity(id);
            const validId = validateId.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.questionaireRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_QUESTIONAIRE_FAILED);
        }
    }
}
