import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { DiseaseModule } from "../modules/diseases/disease.module";
import { DiseaseRequest } from "../modules/diseases/dto/request";

export class DiseaseController {
    private readonly diseaseModule: DiseaseModule;
    public router: Router;
    constructor(diseaseModule: DiseaseModule) {
        this.diseaseModule = diseaseModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/all", this.getAll.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }
    private async getAll(_: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.diseaseModule.getAll();
            res.responseJson(s.SUCCE<PERSON>, r.SUCCE<PERSON>, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as DiseaseRequest;
            const response = await this.diseaseModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as DiseaseRequest;
            const id = req.params.id;
            const response = await this.diseaseModule.update(id, body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.diseaseModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
