import { Request, Response, NextFunction } from "express";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { ErrorTypeEnum as e } from "../enum/errors";
import { KeycloakService } from "../services/keycloak.service";
import { DefaultError, ServiceError } from "./errorHandler";
// Middleware for verify token
export const verifyToken = async (req: Request, _: Response, next: NextFunction) => {
    try {
        const authHeader = req.headers.authorization;
        let authService!: KeycloakService;
        try {
            authService = new KeycloakService("");
        } catch (error) {
            console.error(e.SERVICE_ERROR, error);
            return next(new DefaultError(e.SERVICE_ERROR, m.SERVICE_CONFIGURED_ERROR));
        }
        if (!authHeader?.startsWith("Bearer ")) {
            return next(new DefaultError(e.UNAUTHENTICATED_ERROR, m.INVALID_TOKEN));
        }
        const token = authHeader.split(" ")[1];
        const decodedToken = await authService.tokenDecoder(token);
        req.body.user = decodedToken;
        req.body.token = token;
        next();
    } catch (error) {
        if (error instanceof ServiceError) {
            let errorType;
            switch (error.statusCode) {
                case s.INTERNAL_SERVER_ERROR:
                    errorType = e.INTERNAL_SERVER_ERROR;
                    break;
                case s.UNAUTHENTICATED:
                    errorType = e.UNAUTHENTICATED_ERROR;
                    break;
                default:
                    errorType = e.SERVICE_ERROR;
                    break;
            }
            return next(new DefaultError(errorType, error.message));
        }
        if (error instanceof DefaultError) {
            return next(error);
        }
        return next(new DefaultError(e.INTERNAL_SERVER_ERROR, m.DECODE_TOKEN_ERROR));
    }
};
