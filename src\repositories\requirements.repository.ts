import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { RequirementAttributes } from "../entities/requirements.entity";
import { PetSpeciesEnum } from "../enum/pet";
import { withMongoDB } from "../db/mongodb";
import { RequirementSearchListRequest } from "../modules/requirements/dto/request";

interface RequirementQuery {
    isDeleted: boolean;
    species?: PetSpeciesEnum;
    name?: { $regex: string; $options: string };
}

export class RequirementRepository {
    async findRequirementById(id: string): Promise<RequirementAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RequirementAttributes>("requirements").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(filter: RequirementSearchListRequest): Promise<{ rows: RequirementAttributes[]; count: number }> {
        try {
            return await withMongoDB(async (db) => {
                const { species, name, limit, page } = filter;
                const query: RequirementQuery = { isDeleted: false };

                const offset = (page - 1) * limit;

                if (species) query.species = species;

                // name%
                if (name) query.name = { $regex: `^${name}`, $options: "i" };

                const count = await db.collection<RequirementAttributes>("requirements").countDocuments(query);

                const rows = await db
                    .collection<RequirementAttributes>("requirements")
                    .find(query, {
                        projection: {
                            _id: 0,
                            id: 1,
                            name: 1,
                            species: 1,
                            nutrients: 1,
                        },
                    })
                    .sort({ name: 1 })
                    .skip(offset)
                    .limit(limit)
                    .toArray();

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findRequirementsBySpecieAndNames(species: PetSpeciesEnum, names: string[]): Promise<RequirementAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db
                    .collection<RequirementAttributes>("requirements")
                    .find(
                        { name: { $in: names }, species: species },
                        {
                            projection: {
                                _id: 0,
                            },
                        }
                    )
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAllRequirementsBySpecies(species: PetSpeciesEnum): Promise<RequirementAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const query = { species: species, isDeleted: false };

                const response = await db
                    .collection<RequirementAttributes>("requirements")
                    .find(query, {
                        projection: {
                            _id: 0,
                            id: 1,
                            name: 1,
                        },
                    })
                    .toArray();

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<RequirementAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const response = await db
                    .collection<RequirementAttributes>("requirements")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findExistingRequirementByNameAndSpecies(
        name: string,
        species: PetSpeciesEnum
    ): Promise<RequirementAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RequirementAttributes>("requirements").findOne(
                    {
                        name: name,
                        species: species,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async createRequirements(datas: RequirementAttributes[]): Promise<{ insertedCount: number }> {
        try {
            return await withMongoDB(async (db) => {
                const requirementsData = datas.map((data) => ({
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                }));

                const result = await db.collection<RequirementAttributes>("requirements").insertMany(requirementsData);

                if (!result.insertedCount) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                return { insertedCount: result.insertedCount };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: RequirementAttributes): Promise<RequirementAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const requirementData: RequirementAttributes = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdRequirement = await db
                    .collection<RequirementAttributes>("requirements")
                    .insertOne(requirementData);

                if (!createdRequirement) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                return requirementData;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<RequirementAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RequirementAttributes>("requirements").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RequirementAttributes>("requirements").updateOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new RequirementRepository();
