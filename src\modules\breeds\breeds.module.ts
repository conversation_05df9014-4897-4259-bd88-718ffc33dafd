import { BreedAttributes, BreedEntity } from "../../entities/breeds.entity";
import {
    FormatValidationError,
    DefaultError,
    EntityError,
    ServiceError,
    RepositoryError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import { BreedRequest, BreedSearchRequest } from "./dto/request";
import { BreedListResponse, BreedResponse } from "./dto/response";
import { BreedRepository } from "../../repositories/breeds.repository";

export class BreedModule {
    private readonly breedRepository;
    constructor() {
        this.breedRepository = new BreedRepository();
    }
    public async getOne(id: string): Promise<BreedResponse | null> {
        try {
            const breedEntity = new BreedEntity(id);
            const validId = breedEntity.getId();
            const response = await this.breedRepository.findBreedById(validId);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_BREED_FAILED);
        }
    }

    public async getAll(): Promise<BreedResponse[]> {
        try {
            const response = await this.breedRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_BREED_FAILED);
        }
    }

    public async getAllWithFilter(filter: BreedSearchRequest): Promise<BreedResponse[]> {
        try {
            const response = await this.breedRepository.findAllWithFilter(filter);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_BREED_WITH_FILTER_FAILED);
        }
    }

    public async getList(limit: number, page: number): Promise<BreedListResponse> {
        try {
            const response = await this.breedRepository.findList(limit, page);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_BREED_FAILED);
        }
    }

    public async createMultiple(data: BreedRequest[]): Promise<{ insertedCount: number }> {
        try {
            const validatedBreeds = data.map((breedRequest) => {
                const breedData = breedRequest as BreedAttributes;
                const breedEntity = new BreedEntity(null, breedData);
                return breedEntity.getAttributes();
            });

            const response = await this.breedRepository.createBreeds(validatedBreeds);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_MULTIPLE_BREED_FAILED);
        }
    }

    public async create(data: BreedRequest): Promise<BreedResponse> {
        try {
            const breedData = data as BreedAttributes;
            const breedEntity = new BreedEntity(null, breedData);
            const validData = breedEntity.getAttributes();
            const response = await this.breedRepository.create(validData);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_BREED_FAILED);
        }
    }

    public async update(id: string, data: BreedRequest): Promise<boolean> {
        try {
            const breedData = data as BreedAttributes;
            const breedEntity = new BreedEntity(id, breedData);
            const validId = breedEntity.getId();
            const validData = breedEntity.getAttributes();
            const response = await this.breedRepository.update(validId, validData);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_BREED_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const validateId = new BreedEntity(id);
            const validId = validateId.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.breedRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_BREED_FAILED);
        }
    }
}
