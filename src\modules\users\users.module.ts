import { UserEntity, UserAttributes } from "../../entities/users.entity";
import { RoleEntity } from "../../entities/roles.entity";
import { PermissionEntity } from "../../entities/permissions.entity";
import { FormatValidationError, DefaultError, ServiceError } from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e } from "../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../enum/response";
import { UserRequest, UserFilterRequest, PermissionRequest } from "./dto/request";
import { UserResponse, UserListResponse, CredentialsResponse } from "./dto/response";
import { RolesResponse } from "../roles/dto/response";
import { PermissionsResponse } from "../permissions/dto/response";
import { KeycloakService } from "../../services/keycloak.service";
import { receiveConfig } from "../../validations/users/users.validate.config";
import { receiveConfig as roleReceiveConfig } from "../../validations/roles/roles.validate.config";
import { receiveConfig as permissionReceiveConfig } from "../../validations/permissions/permissions.validate.config";

export class UserModule {
    private authService!: KeycloakService;
    private initializedAuthService(token: string): void {
        try {
            this.authService = new KeycloakService(token);
        } catch (error) {
            console.error(e.SERVICE_ERROR, error);
            throw new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_CONFIGURED_ERROR);
        }
    }

    public async getAll(token: string): Promise<UserResponse[]> {
        try {
            this.initializedAuthService(token);
            const users = await this.authService.getAllUsers();
            const usersResponse = users.map((user) => {
                const userAttributes: UserAttributes = {
                    createdAt: new Date(user.createdTimestamp),
                    ...user,
                };
                const userEntity = new UserEntity(userAttributes.id, userAttributes, receiveConfig);
                const userResponse = userEntity.getUserResponse();
                return userResponse;
            });
            return usersResponse as UserResponse[];
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_USER_FAILED);
        }
    }

    public async getOne(token: string, id: string): Promise<UserResponse> {
        try {
            const userEntity = new UserEntity(id, null);
            const userId = userEntity.getId();
            this.initializedAuthService(token);
            const user = await this.authService.getUserById(userId);
            const userAttributes: UserAttributes = {
                createdAt: new Date(user.createdTimestamp),
                ...user,
            };
            userEntity.setAttributes(userAttributes, receiveConfig);
            const userResponse = userEntity.getUserResponse();
            return userResponse as UserResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, null, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_USER_FAILED);
        }
    }

    public async getList(
        token: string,
        limit: number,
        page: number,
        filter?: UserFilterRequest
    ): Promise<UserListResponse> {
        try {
            // get users
            this.initializedAuthService(token);
            const userEntity = new UserEntity(null, null);
            if (filter) userEntity.validateFilter(filter);
            const users = await this.authService.searchUser(filter);
            const usersResponse = users.map((user) => {
                const userAttributes: UserAttributes = {
                    createdAt: new Date(user.createdTimestamp),
                    ...user,
                };
                userEntity.setAttributes(userAttributes, receiveConfig);
                const userResponse = userEntity.getUserResponse();
                return userResponse as UserResponse;
            });
            // pagination
            let offset: number = 0;
            let usersResponseWithPagination = usersResponse;
            if (limit && page) {
                offset = limit * (page - 1);
                usersResponseWithPagination = usersResponse.slice(offset, limit * page);
            }
            const userListResponse = {
                rows: usersResponseWithPagination,
                count: usersResponse.length,
            };
            return userListResponse as UserListResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_USER_FAILED);
        }
    }

    public async create(token: string, data: UserRequest): Promise<CredentialsResponse> {
        try {
            const userEntity = new UserEntity(null, data as UserAttributes);
            this.initializedAuthService(token);
            const credential = this.authService.generateUserCredential();
            const credentialAttributes = {
                type: credential?.type?.toUpperCase(),
                value: credential?.value,
                temporary: credential?.temporary,
            };
            userEntity.setCredential(credentialAttributes);
            const user = userEntity.getUserRepresentation();
            await this.authService.createUser(user);

            const credentialsResponse = {
                username: user.username,
                password: credential.value,
            };
            return credentialsResponse;
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_USER_FAILED);
        }
    }

    public async update(token: string, id: string, user: UserRequest): Promise<void> {
        try {
            const userAttributes = { id, ...user };
            const userEntity = new UserEntity(id, userAttributes, receiveConfig);
            this.initializedAuthService(token);
            const userId = userEntity.getId();
            const userData = userEntity.getUserRepresentation();
            await this.authService.updateUser(userId, userData);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_USER_FAILED);
        }
    }

    public async delete(token: string, id: string): Promise<void> {
        try {
            const userEntity = new UserEntity(id, null);
            this.initializedAuthService(token);
            const userId = userEntity.getId();
            await this.authService.deleteUser(userId);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_USER_FAILED);
        }
    }

    public async getRoles(token: string, id: string): Promise<RolesResponse[]> {
        try {
            const userEntity = new UserEntity(id, null);
            this.initializedAuthService(token);
            const userId = userEntity.getId();
            const roles = await this.authService.getUserGroups(userId);
            return roles.map((role) => {
                const roleEntity = new RoleEntity(role.id, role, roleReceiveConfig);
                const roleResponse = roleEntity.getRoleResponse();
                return roleResponse;
            });
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_USER_ROLES_FAILED);
        }
    }

    public async getPermissions(token: string, id: string): Promise<PermissionsResponse[]> {
        try {
            const userEntity = new UserEntity(id, null);
            this.initializedAuthService(token);
            const userId = userEntity.getId();
            const clientRoles = await this.authService.getClientRoleMappingsForUser(userId);
            return clientRoles.map((role) => {
                const permissionEntity = new PermissionEntity(role.name, role, permissionReceiveConfig);
                const permissionResponse = permissionEntity.getPermissionAttributes();
                return permissionResponse;
            });
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_USER_PERMISSIONS_FAILED);
        }
    }

    public async addPermission(token: string, id: string, permission: PermissionRequest): Promise<void> {
        try {
            const userEntity = new UserEntity(id, null);
            const permissionEntity = new PermissionEntity(permission.name, permission, permissionReceiveConfig);
            this.initializedAuthService(token);
            const userId = userEntity.getId();
            const permissionData = permissionEntity.getPermissionAttributes();
            await this.authService.addClientRoleToUser(userId, [permissionData]);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.ADD_USER_PERMISSION_FAILED);
        }
    }

    public async removePermission(token: string, id: string, permission: PermissionRequest): Promise<void> {
        try {
            const userEntity = new UserEntity(id, null);
            const permissionEntity = new PermissionEntity(permission.name, permission, permissionReceiveConfig);
            this.initializedAuthService(token);
            const userId = userEntity.getId();
            const permissionData = permissionEntity.getPermissionAttributes();
            await this.authService.removeClientRoleFromUser(userId, [permissionData]);
        } catch (error) {
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.REMOVE_USER_PERMISSION_FAILED);
        }
    }
}
