import { FieldConfigMap } from "../../types/validate.type";
import { NutrientValidateType } from "./nutrients.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { NutrientTypeEnum } from "../../enum/requirement";

export const NutrientConfig: FieldConfigMap<NutrientValidateType> = {
    nutrientName: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 2, maxLength: 80 },
    unit: { type: v.STRING, required: true, minLength: 1, maxLength: 20 },
    type: { type: v.ENUM, required: true, enumValues: Object.values(NutrientTypeEnum) },
    value: { type: v.NUMBER, required: true, min: 0, max: 999999 },
};
