import { DoctorAttributes } from "../../../entities/doctors.entity";
import { DoctorRequest } from "../../../modules/doctors/dto/request";
import { v7 as uuidv7 } from "uuid";
import { ErrorTypeEnum as e, DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";

const ValidDoctorInfo: DoctorRequest = {
    prefix: "Mr.",
    firstName: "<PERSON>",
    lastName: "Doe",
    phoneNumber: "0912345678",
    email: "<EMAIL>",
};
const ValidDoctorAttributes: DoctorAttributes = {
    id: uuidv7(),
    doctorInfo: {
        prefix: "Mr.",
        firstName: "<PERSON>",
        lastName: "<PERSON><PERSON>",
        phoneNumber: "0912345678",
        email: "<EMAIL>",
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
};
const ValidDoctorAttributes2: DoctorAttributes = {
    id: uuidv7(),
    doctorInfo: {
        prefix: "Mr.",
        firstName: "Robin",
        lastName: "Hood",
        phoneNumber: "0812345679",
        email: "<EMAIL>",
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    isDeleted: false,
    deletedAt: null,
};
const InvalidDoctorAttributes = {
    id: "1",
    doctorInfo: {
        prefix: "1",
        firstName: "1",
        lastName: "1",
        phoneNumber: "1",
        email: "1",
    },
    createdAt: "1",
    updatedAt: "1",
    isDeleted: "1",
    deletedAt: "1",
};
const InvalidDoctorInfo = {
    empty: {},
    invalidValue: {
        prefix: "1",
        firstName: "1",
        lastName: "1",
        phoneNumber: "1",
        email: "1",
    },
    invalidKey: {
        somethingElse: "value",
    } as any as DoctorRequest,
};
export const createMocks = {
    request: {
        validBody: {
            ...ValidDoctorInfo,
        },
        validBodyWithoutEmail: {
            prefix: "Mr.",
            firstName: "John",
            lastName: "Doe",
            phoneNumber: "0912345678",
        },
        invalidBodyValue: {
            ...InvalidDoctorInfo.invalidValue,
        },
        missingBodyKey: {
            ...InvalidDoctorInfo.invalidKey,
        },
        emptyBody: {
            ...InvalidDoctorInfo.empty,
        },
    },
    serviceResponse: {
        success: {
            data: void 0,
        },
        empty: {
            data: null,
        },
        duplicateName: {
            data: { ...ValidDoctorAttributes, doctorInfo: { ...ValidDoctorInfo, firstName: "John", lastName: "Doe" } },
        },
        duplicatePhoneNumber: {
            data: { ...ValidDoctorAttributes, doctorInfo: { ...ValidDoctorInfo, phoneNumber: "0912345678" } },
        },
        duplicateEmail: {
            data: { ...ValidDoctorAttributes, doctorInfo: { ...ValidDoctorInfo, email: "<EMAIL>" } },
        },
        duplicateData: {
            data: { ...ValidDoctorAttributes, doctorInfo: { ...ValidDoctorInfo } },
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidBodyValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                prefix: v.PREFIX,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                phoneNumber: v.PHONE,
                email: v.EMAIL,
            },
        },
        missingBodyRequiredKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                prefix: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                phoneNumber: v.REQUIRED,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_DOCTOR_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        duplicateName: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
            },
            statusCode: s.CONFLICT,
        },
        duplicatePhoneNumber: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                phoneNumber: m.DUPLICATED_PHONE_NUMBER,
            },
            statusCode: s.CONFLICT,
        },
        duplicateEmail: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                email: m.DUPLICATED_EMAIL,
            },
            statusCode: s.CONFLICT,
        },
        duplicateData: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
                phoneNumber: m.DUPLICATED_PHONE_NUMBER,
                email: m.DUPLICATED_EMAIL,
            },
            statusCode: s.CONFLICT,
        },
    },
};
export const getAllMocks = {
    serviceResponse: {
        success: {
            data: [ValidDoctorAttributes, ValidDoctorAttributes2],
        },
        empty: {
            data: [],
        },
        missingKey: {
            data: [
                {
                    somethingElse: "value",
                } as any as DoctorAttributes,
            ],
        },
        missingInfoKey: {
            data: [
                {
                    ...ValidDoctorAttributes,
                    doctorInfo: {
                        somethingElse: "value",
                    },
                } as any as DoctorAttributes,
            ],
        },
        invalidValue: {
            data: [InvalidDoctorAttributes],
        },
    },
    response: {
        success: {
            data: [ValidDoctorAttributes, ValidDoctorAttributes2],
        },
        empty: {
            data: [],
        },
    },
    error: {
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_DOCTORS_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        missingKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                doctorInfo: v.REQUIRED,
                createdAt: v.REQUIRED,
                updatedAt: v.REQUIRED,
                isDeleted: v.REQUIRED,
                deletedAt: v.REQUIRED,
            },
        },
        missingInfoKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                doctorInfo: {
                    prefix: v.REQUIRED,
                    firstName: v.REQUIRED,
                    lastName: v.REQUIRED,
                    phoneNumber: v.REQUIRED,
                    email: v.REQUIRED,
                },
            },
        },
        invalidValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
                doctorInfo: {
                    prefix: v.PREFIX,
                    firstName: v.LETTER_ONLY,
                    lastName: v.LETTER_ONLY,
                    phoneNumber: v.PHONE,
                    email: v.EMAIL,
                },
                createdAt: v.DATE,
                updatedAt: v.DATE,
                isDeleted: v.BOOLEAN,
                deletedAt: v.DATE,
            },
        },
    },
};
export const updateMocks = {
    request: {
        validId: {
            id: ValidDoctorAttributes.id,
        },
        invalidId: {
            id: "invalidId",
        },
        validBody: {
            ...ValidDoctorInfo,
        },
        validBodyWithoutEmail: {
            prefix: "Mr.",
            firstName: "John",
            lastName: "Doe",
            phoneNumber: "0912345678",
        },
        invalidBodyValue: {
            ...InvalidDoctorInfo.invalidValue,
        },
        missingBodyRequiredKey: {
            ...InvalidDoctorInfo.invalidKey,
        },
        emptyBody: {
            ...InvalidDoctorInfo.empty,
        },
    },
    serviceResponse: {
        success: {
            data: void 0,
        },
        empty: {
            data: null,
        },
        duplicateName: {
            data: { ...ValidDoctorAttributes2, doctorInfo: { ...ValidDoctorInfo, firstName: "John", lastName: "Doe" } },
        },
        duplicatePhoneNumber: {
            data: { ...ValidDoctorAttributes2, doctorInfo: { ...ValidDoctorInfo, phoneNumber: "0912345678" } },
        },
        duplicateEmail: {
            data: { ...ValidDoctorAttributes2, doctorInfo: { ...ValidDoctorInfo, email: "<EMAIL>" } },
        },
        duplicateData: {
            data: { ...ValidDoctorAttributes2, doctorInfo: { ...ValidDoctorInfo } },
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidId: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        invalidBodyValue: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                prefix: v.PREFIX,
                firstName: v.LETTER_ONLY,
                lastName: v.LETTER_ONLY,
                phoneNumber: v.PHONE,
                email: v.EMAIL,
            },
        },
        missingBodyRequiredKey: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                prefix: v.REQUIRED,
                firstName: v.REQUIRED,
                lastName: v.REQUIRED,
                phoneNumber: v.REQUIRED,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.UPDATE_DOCTOR_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        duplicateName: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
            },
            statusCode: s.CONFLICT,
        },
        duplicatePhoneNumber: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                phoneNumber: m.DUPLICATED_PHONE_NUMBER,
            },
            statusCode: s.CONFLICT,
        },
        duplicateEmail: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                email: m.DUPLICATED_EMAIL,
            },
            statusCode: s.CONFLICT,
        },
        duplicateData: {
            errorType: e.DATABASE_ERROR,
            message: m.DUPLICATED_DATA,
            data: null,
            error: {
                name: m.DUPLICATED_NAME,
                phoneNumber: m.DUPLICATED_PHONE_NUMBER,
                email: m.DUPLICATED_EMAIL,
            },
            statusCode: s.CONFLICT,
        },
        notFound: {
            errorType: e.DATABASE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: d.RECORD_NOT_FOUND,
        },
    },
};
export const deleteMocks = {
    request: {
        validId: {
            id: ValidDoctorAttributes.id,
        },
        invalidId: {
            id: "invalidId",
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        invalidId: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.UUID,
            },
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.DELETE_DOCTOR_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        notFound: {
            errorType: e.DATABASE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: d.RECORD_NOT_FOUND,
        },
    },
};
