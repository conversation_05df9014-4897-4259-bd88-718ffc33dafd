import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { QuestionaireModule } from "../modules/questionaires/questionaires.module";
import { QuestionaireRequest } from "../modules/questionaires/dto/request";
import { PetSpeciesEnum } from "src/enum/pet";

export class QuestionaireController {
    private readonly questionaireModule: QuestionaireModule;
    public router: Router;
    constructor(questionaireModule: QuestionaireModule) {
        this.questionaireModule = questionaireModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/species/:species/type/:type", this.getOneBySpeciesAndType.bind(this));
        this.router.get("/:id", this.getOneById.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }
    private async getOneById(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.questionaireModule.getOneById(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getOneBySpeciesAndType(req: Request, res: Response, next: NextFunction) {
        try {
            const species = req.params.species as PetSpeciesEnum;
            const type = req.params.type;
            const response = await this.questionaireModule.getOneBySpeciesAndType(species, type);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.questionaireModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as QuestionaireRequest;
            const response = await this.questionaireModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body as QuestionaireRequest;
            const response = await this.questionaireModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.questionaireModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
