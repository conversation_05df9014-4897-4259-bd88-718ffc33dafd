import { PetSpeciesEnum } from "../../enum/pet";
import { NutrientTypeEnum } from "../../enum/requirement";

interface NutrientDetailRange {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
}

export interface RequirementValidateType {
    id?: string;
    name: string;
    species: PetSpeciesEnum;
    nutrients: NutrientDetailRange[];
}

export interface RequirementRecipeMatchingValidateType {
    nutrients: NutrientDetailRange[];
}
