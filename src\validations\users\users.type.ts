export interface UserValidateType {
    id?: string;
    username: string;
    firstName: string;
    lastName: string;
    email: string;
    emailVerified?: boolean;
    createdAt?: Date;
    enabled?: boolean;
    roles?: string[];
}
export interface UserCredentialsValidateType {
    type: string;
    value: string;
    temporary?: boolean;
}
export interface UserFilterRequest {
    username?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
}
