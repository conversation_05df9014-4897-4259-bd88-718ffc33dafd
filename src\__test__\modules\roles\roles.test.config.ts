import { TestCase } from "../../utils/testRunner";
import { RoleModule } from "../../../modules/roles/roles.module";
import { RolesRequest } from "../../../modules/roles/dto/request";
import { RolesResponse, RolesListResponse } from "../../../modules/roles/dto/response";
import { getAllMocks, getOneMocks, getListMocks, createMocks, updateMocks, deleteMocks } from "./roles.test.mock";
import { DefaultError, ServiceError } from "../../../middlewares/errorHandler";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../../../enum/response";
import { mockService as userMockService } from "../users/users.test.config";

type MethodTypeMapper = {
    getOne: { input: { token: string; id: string }; output: RolesResponse };
    getAll: { input: { token: string }; output: RolesResponse[] };
    getList: {
        input: { token: string; limit: number; page: number };
        output: RolesListResponse;
    };
    create: { input: { token: string; data: RolesRequest }; output: void };
    update: { input: { token: string; id: string; data: RolesRequest }; output: void };
    delete: { input: { token: string; id: string }; output: void };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof RoleModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

// Mock repository object
export const mockService = {
    ...userMockService,
    getGroups: jest.fn(),
    getGroupById: jest.fn(),
    createGroup: jest.fn(),
    updateGroup: jest.fn(),
    deleteGroup: jest.fn(),
};
// Replace the real Repository or Service class with our mock version
jest.mock("../../../services/keycloak.service", () => ({
    KeycloakService: jest.fn(() => mockService),
}));

const moduleInstance = new RoleModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Roles",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all roles",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getAllMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return all roles in case of dto response",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getAllMocks.response.successWithoutDto);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no roles exist",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getAllMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: getAllMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: async () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },

                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: getAllMocks.request.invalidToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getAllMocks.request.forbiddenToken,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getAllMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getAllMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getAllMocks.request.validToken,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.token);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getOne: {
            cases: [
                {
                    name: "[SUCCESS] should return one role",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getGroupById.mockResolvedValue(getOneMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: getOneMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return one role in case of dto response",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getGroupById.mockResolvedValue(getOneMocks.response.successWithoutDto);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: getOneMocks.response.success,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: async () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid uuid occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.invalidParams.id,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getGroupById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: getOneMocks.request.invalidToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getGroupById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getOneMocks.request.forbiddenToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getGroupById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when role not found",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.getGroupById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getGroupById.mockResolvedValue(getOneMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.getGroupById.mockResolvedValue(getOneMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getOneMocks.request.validToken,
                            id: getOneMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getGroupById.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getOne(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getOneMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroupById,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return list of roles",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getListMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.success,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of roles in case of dto response",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getListMocks.response.successWithoutDto);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.success,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of roles in case of pagination",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimitOne,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getListMocks.response.success);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.successWithPagination,
                            count: getListMocks.response.success.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list when no roles exist",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: {
                            rows: getListMocks.response.empty,
                            count: getListMocks.response.empty.length,
                        },
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: async () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: getListMocks.request.invalidToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: getListMocks.request.forbiddenToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getListMocks.response.invalidKey);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            mockService.getGroups.mockResolvedValue(getListMocks.response.invalidValue);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: getListMocks.request.validToken,
                            limit: getListMocks.request.validLimit,
                            page: getListMocks.request.validPage,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.getGroups.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.token, input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getGroups,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should create a role",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.createGroup.mockResolvedValue(createMocks.response.success.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: async () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.createGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: createMocks.request.invalidToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.createGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: createMocks.request.forbiddenToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.createGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.createGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: {
                            onSuccess: (result: any) => {
                                expect(result).toBeInstanceOf(result.instance);
                                expect(result).toMatchObject(result.properties);
                            },
                        },
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid value occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.invalidBodyValue,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator missing key occurs",
                    setup: {
                        input: {
                            token: createMocks.request.validToken,
                            data: createMocks.request.missingBodyKey,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.token, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingKeyError,
                        },
                        methodCalls: [
                            {
                                method: mockService.createGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        update: {
            cases: [
                {
                    name: "[SUCCESS] should update a role",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.updateGroup.mockResolvedValue(updateMocks.response.success.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: async () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw unavailable error when service unavailable occur",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.updateGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authentication error for unauthenticated user",
                    setup: {
                        input: {
                            token: updateMocks.request.invalidToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.updateGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: updateMocks.request.forbiddenToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.updateGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when role not found",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.updateGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator UUID occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.invalidParams.id,
                            data: updateMocks.request.validBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator name occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.invalidBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidNameError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator id and name occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.invalidParams.id,
                            data: updateMocks.request.invalidBody,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.invalidValueError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: updateMocks.request.validToken,
                            id: updateMocks.request.validParams.id,
                            data: updateMocks.request.validBody,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.updateGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.update(input.token, input.id, input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: updateMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.updateGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        delete: {
            cases: [
                {
                    name: "[SUCCESS] should delete a role",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            mockService.deleteGroup.mockResolvedValue(deleteMocks.response.success.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: undefined,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service initialization fails",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: async () => {
                            jest.spyOn(
                                require("../../../services/keycloak.service"),
                                "KeycloakService"
                            ).mockImplementationOnce(() => {
                                throw new Error();
                            });
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.serviceInitializationError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw service error when service unavailable occur",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_UNAVAILABLE);
                            mockService.deleteGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.serviceUnavailable,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authenticated error for unauthenticated user",
                    setup: {
                        input: {
                            token: deleteMocks.request.invalidToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
                            mockService.deleteGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw authorization error for forbidden user",
                    setup: {
                        input: {
                            token: deleteMocks.request.forbiddenToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
                            mockService.deleteGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.forbiddenToken,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when validator invalid uuid occurs",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.invalidParams.id,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.invalidUuidError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw not found error when role not found",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
                            mockService.deleteGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.notFound,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw internal server error when unexpected error occurs",
                    setup: {
                        input: {
                            token: deleteMocks.request.validToken,
                            id: deleteMocks.request.validParams.id,
                        },
                        mocks: () => {
                            const error = new Error("UnknowError");
                            mockService.deleteGroup.mockRejectedValue(error);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.delete(input.token, input.id);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: deleteMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.deleteGroup,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
