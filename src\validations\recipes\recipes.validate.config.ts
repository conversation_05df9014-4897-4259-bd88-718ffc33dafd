import { FieldConfigMap } from "../../types/validate.type";
import { RecipeValidateType } from "./recipes.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { RecipeIngredientEnum } from "../../enum/recipe";

export const createConfig: FieldConfigMap<RecipeValidateType> = {
    name: { type: v.ALL_CHARS, required: true },
    ingredients: { type: v.ENUM, required: true, enumValues: Object.values(RecipeIngredientEnum), array: true },
    nutrients: {
        type: v.NUTRIENT_RECIPE,
        required: true,
    },
};
