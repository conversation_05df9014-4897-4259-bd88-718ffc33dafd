import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { RecipeAttributes } from "../entities/recipes.entity";
import { withMongoDB } from "../db/mongodb";

export class RecipeRepository {
    async findRecipeById(id: string): Promise<RecipeAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RecipeAttributes>("recipes").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findRecipeByName(name: string): Promise<RecipeAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RecipeAttributes>("recipes").findOne(
                    {
                        name: name,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findExistingRecipeByName(name: string): Promise<RecipeAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RecipeAttributes>("recipes").findOne(
                    {
                        name: name,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findExistingRecipeByNames(names: string[]): Promise<RecipeAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db
                    .collection<RecipeAttributes>("recipes")
                    .find(
                        { name: { $in: names } },
                        {
                            projection: {
                                _id: 0,
                            },
                        }
                    )
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(limit: number, page: number): Promise<{ rows: RecipeAttributes[]; count: number }> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const offset = (page - 1) * limit;

                const count = await db.collection<RecipeAttributes>("recipes").countDocuments(filter);

                const rows = await db
                    .collection<RecipeAttributes>("recipes")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .sort({ name: 1 })
                    .skip(offset)
                    .limit(limit)
                    .toArray();

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<RecipeAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const response = await db
                    .collection<RecipeAttributes>("recipes")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async createRecipes(datas: RecipeAttributes[]): Promise<{ insertedCount: number }> {
        try {
            return await withMongoDB(async (db) => {
                const recipesData = datas.map((data) => ({
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                }));

                const result = await db.collection<RecipeAttributes>("recipes").insertMany(recipesData);

                if (!result.insertedCount) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                return { insertedCount: result.insertedCount };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: RecipeAttributes): Promise<RecipeAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const recipeData: RecipeAttributes = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdRecipe = await db.collection<RecipeAttributes>("recipes").insertOne(recipeData);

                if (!createdRecipe) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                return recipeData;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<RecipeAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RecipeAttributes>("recipes").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<RecipeAttributes>("recipes").updateOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new RecipeRepository();
