import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { PermissionModule } from "../modules/permissions/permissions.module";
import { ListLimitConstants } from "../constants/defaultValue";
import { requirePermission } from "../middlewares/verifyPermission";
import { PermissionEnum as p } from "../enum/permission";

export class PermissionController {
    private readonly permissionModule: PermissionModule;
    public router: Router;
    constructor(permissionModule: PermissionModule) {
        this.permissionModule = permissionModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.use(requirePermission(p.READ_PERMISSION));
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/:name", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));

        this.router.use(requirePermission(p.MANAGE_PERMISSION));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:name", this.update.bind(this));
        this.router.delete("/:name", this.delete.bind(this));
    }

    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const response = await this.permissionModule.getAll(token);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const name = req.params.name;
            const token = req.body.token;
            const response = await this.permissionModule.getOne(token, name);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const listLimit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            const listPage = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;
            const response = await this.permissionModule.getList(token, listLimit, listPage);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const permission = req.body;
            const response = await this.permissionModule.create(token, permission);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const name = req.params.name;
            const permission = req.body;
            const response = await this.permissionModule.update(token, name, permission);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const name = req.params.name;
            const response = await this.permissionModule.delete(token, name);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
