import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { UserModule } from "../modules/users/users.module";
import { UserFilterRequest, PermissionRequest } from "../modules/users/dto/request";
import { ListLimitConstants } from "../constants/defaultValue";
import { requirePermission } from "../middlewares/verifyPermission";
import { PermissionEnum as p } from "../enum/permission";

export class UserController {
    private readonly userModule: UserModule;
    public router: Router;

    constructor(userModule: UserModule) {
        this.userModule = userModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.use(requirePermission(p.VIEW_USER));
        this.router.get("/:id/roles", this.getRoles.bind(this));
        this.router.get("/:id/permissions", this.getPermissions.bind(this));
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));

        this.router.use(requirePermission(p.MANAGE_USER));
        this.router.post("/:id/permissions", this.addPermission.bind(this));
        this.router.delete("/:id/permissions", this.removePermission.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }
    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const token = req.body.token;
            const response = await this.userModule.getOne(token, id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const response = await this.userModule.getAll(token);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const userFilterRequest: UserFilterRequest = {
                username: req.query.username as string,
                firstName: req.query.firstName as string,
                lastName: req.query.lastName as string,
                email: req.query.email as string,
            };

            const listLimit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;

            const listPage = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;

            const response = await this.userModule.getList(token, listLimit, listPage, userFilterRequest);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const user = req.body;
            const response = await this.userModule.create(token, user);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const user = req.body;
            await this.userModule.update(token, id, user);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, null, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            await this.userModule.delete(token, id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, null, null);
        } catch (error) {
            next(error);
        }
    }
    private async getRoles(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const response = await this.userModule.getRoles(token, id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getPermissions(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const response = await this.userModule.getPermissions(token, id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async addPermission(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const roleAttributes: PermissionRequest = {
                id: req.body.id,
                name: req.body.name,
            };
            await this.userModule.addPermission(token, id, roleAttributes);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, null, null);
        } catch (error) {
            next(error);
        }
    }
    private async removePermission(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const roleAttributes: PermissionRequest = {
                id: req.body.id,
                name: req.body.name,
            };
            await this.userModule.removePermission(token, id, roleAttributes);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, null, null);
        } catch (error) {
            next(error);
        }
    }
}
