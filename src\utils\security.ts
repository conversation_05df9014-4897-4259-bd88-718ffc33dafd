/**
 * Security utility functions for preventing NoSQL injection attacks
  - . → \.
  - * → \*
  - + → \+
  - ? → \?
  - ^ → \^
  - $ → \$
  - { and } → \{ and \}
  - ( and ) → \( and \)
  - | → \|
  - [ and ] → \[ and \]
  - \ → \\
 */
export const escapeRegexSpecialChars = (input: string): string => {
    return input.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
};

/**
 * SecurityUtil class for centralized input sanitization and validation
 * Provides methods for secure handling of different data types
 */
export class Sanitizer {
    private static readonly UUID_REGEX = /^[0-9a-f]{8}-[0-9a-f]{4}-[47][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    private static readonly PHONE_REGEX = /^(08|09|06)\d{8}$|^(02)\d{7}$/;
    private static readonly EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;


    public static sanitizeForRegex(input: string): string {
        return input.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    }

    /**
     * Sanitizes UUID input to remove all characters except valid UUID characters
     * @param input - UUID string to sanitize
     * @returns Sanitized UUID string
     */
    public static sanitizeUUID(input: string): string {
        return input.trim().replace(/[^0-9a-f-]/gi, '');
    }

    /**
     * Sanitizes text for search queries
     * @param input - Text to sanitize
     * @returns Sanitized text safe for search operations
     */
    public static sanitizeForSearch(input: string): string {
        return input.trim().replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    }

    /**
     * Sanitizes phone number input while preserving valid phone characters
     * Preserves: 0-9 + - () spaces (valid phone characters)
     * Escapes: . * ? ^ $ {} | [] \ (harmful regex characters)
     * @param input - Phone number to sanitize
     * @returns Sanitized phone number
     */
    public static sanitizePhoneNumber(input: string): string {
        return input.trim().replace(/[.*?^${}|[\]\\]/g, "\\$&");
    }

    /**
     * Sanitizes email input while preserving valid email characters
     * Preserves: @ . _ - + (valid email characters)
     * Escapes: * ? ^ $ {} () | [] \ (harmful regex characters)
     * @param input - Email to sanitize
     * @returns Sanitized email
     */
    public static sanitizeEmail(input: string): string {
        // Escape only regex special chars that aren't valid in emails
        // Preserve: @ . _ - +
        return input.trim().toLowerCase().replace(/[*?^${}()|[\]\\]/g, "\\$&");
    }

    /**
     * Validates UUID format without sanitization
     * @param input - UUID string to validate
     * @returns True if valid UUID format
     */
    public static validateUUID(input: string): boolean {
        return this.UUID_REGEX.test(input);
    }

    /**
     * Validates phone number format
     * @param input - Phone number to validate
     * @returns True if valid Thai phone number format
     */
    public static validatePhone(input: string): boolean {
        return this.PHONE_REGEX.test(input);
    }

    /**
     * Validates email format
     * @param input - Email to validate
     * @returns True if valid email format
     */
    public static validateEmail(input: string): boolean {
        return this.EMAIL_REGEX.test(input);
    }
}
