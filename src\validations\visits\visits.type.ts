import { VisitStatusEnum, VisitStepEnum } from "../../enum/visit";
import { PetHealthSubmitType, PetRequirementSubmitType, PetNutritionSubmitType, PetProductMatchSubmitType } from "../../entities/visits.entity";
import { PetInfo } from "../../entities/pets.entity";
import { DoctorInfo } from "../../entities/doctors.entity";
export interface VisitValidateType {
    id?: string;
    petId: string;
    doctorId: string;
    petInfo: PetInfo;
    doctorInfo: DoctorInfo;
    visitStep: VisitStepEnum;
    status: VisitStatusEnum;
    petHealth: PetHealthSubmitType;
    petRequirement: PetRequirementSubmitType;
    petNutrition: PetNutritionSubmitType;
    petProductMatch: PetProductMatchSubmitType;
}

export interface PetHealthSubmitValidateType {
    petHealth: PetHealthSubmitType;
}

export interface PetRequirementSubmitValidateType {
    petRequirement: PetRequirementSubmitType;
}

export interface PetNutritionSubmitValidateType {
    petNutrition: PetNutritionSubmitType;
}

export interface PetProductMatchSubmitValidateType {
    petProductMatch: PetProductMatchSubmitType;
}

export interface GetVisitListFilterRequest {
    petHnId?: string;
    petName?: string;
    ownerName?: string;
    doctorId?: string;
    dateFrom?: string;
    dateTo?: string;
}
