import { FieldConfigType } from "../types/validate.type";
import { validated } from "../utils/validations";
import { createConfig, userCredentialsConfig, filterConfig } from "../validations/users/users.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { ResponseMessageEnum as m } from "../enum/response";
import { FormatValidationError } from "../middlewares/errorHandler";
import { UserRepresentation, CredentialRepresentation } from "../services/keycloak.service";
import { UserFilterRequest } from "../modules/users/dto/request";

export type UserAttributes = {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    email: string;
    emailVerified?: boolean;
    createdAt?: Date;
    enabled?: boolean;
    roles?: string[];
    credentials?: UserCredentials[];
};

type UserCredentials = {
    type: string;
    value: string;
    temporary?: boolean;
};

export class UserEntity {
    private readonly id: string;
    private userData: UserAttributes;

    constructor(id: string | null, userData: UserAttributes | null, customConfig?: Record<string, FieldConfigType>) {
        this.id = id!;
        this.userData = this.initialize(userData, customConfig);
    }

    private initialize(preData: UserAttributes | null, customConfig?: Record<string, FieldConfigType>): UserAttributes {
        this.validate(preData, customConfig);
        return {
            id: this.id ?? preData?.id ?? null,
            ...preData,
        } as UserAttributes;
    }

    private validate(preData: UserAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateUserData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateUserData(data: UserAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public validateFilter(filter: UserFilterRequest): void {
        const result = validated(filterConfig, filter);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.userData.id;
    }

    public getUserResponse(): UserAttributes {
        return {
            id: this.getId(),
            username: this.userData.username,
            firstName: this.userData.firstName,
            lastName: this.userData.lastName,
            email: this.userData.email,
            emailVerified: this.userData.emailVerified,
            createdAt: this.userData.createdAt,
            enabled: this.userData.enabled,
        } as UserAttributes;
    }

    public getUserRepresentation(): UserRepresentation {
        return {
            username: this.userData.username,
            firstName: this.userData.firstName,
            lastName: this.userData.lastName,
            email: this.userData.email,
            emailVerified: this.userData.emailVerified,
            enabled: this.userData.enabled,
            credentials: this.userData.credentials as CredentialRepresentation[],
            groups: this.userData.roles,
        } as UserRepresentation;
    }

    public setAttributes(userData: UserAttributes, customConfig?: Record<string, FieldConfigType>): void {
        this.userData = this.initialize(userData, customConfig);
    }

    public setCredential(credential: UserCredentials): void {
        const validationResult = validated(userCredentialsConfig, credential);
        if (!validationResult.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, validationResult.data);
        }
        this.userData.credentials = [credential];
    }
}
