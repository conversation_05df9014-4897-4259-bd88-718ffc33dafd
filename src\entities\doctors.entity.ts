import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { validated } from "../utils/validations";
import { FormatValidationError } from "../middlewares/errorHandler";
import { createConfig, receiveConfig } from "../validations/doctors/doctors.validate.config";
import { ResponseMessageEnum as m } from "../enum/response";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";

export type DoctorInfo = {
    prefix: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string | null;
};
export type DoctorAttributes = {
    id: string;
    doctorInfo: DoctorInfo;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
    deletedAt: Date | null;
};
export class DoctorEntity {
    private readonly id: string;
    private readonly doctorData: DoctorAttributes;
    constructor(id: string | null, preData: DoctorAttributes | null, customConfig?: Record<string, FieldConfigType>) {
        this.id = id!;
        this.doctorData = this.initialize(preData, customConfig);
    }

    private initialize(
        preData: DoctorAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): DoctorAttributes {
        this.validate(preData, customConfig);
        return {
            id: this.id ?? uuidv7(),
            doctorInfo: {
                prefix: preData?.doctorInfo?.prefix,
                firstName: preData?.doctorInfo?.firstName,
                lastName: preData?.doctorInfo?.lastName,
                phoneNumber: preData?.doctorInfo?.phoneNumber,
                email: preData?.doctorInfo?.email,
            },
            createdAt: preData?.createdAt,
            updatedAt: preData?.updatedAt,
            isDeleted: preData?.isDeleted,
            deletedAt: preData?.deletedAt,
        } as DoctorAttributes;
    }

    private validate(preData: DoctorAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateDoctorData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }
    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    private validateDoctorData(data: DoctorAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? receiveConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    private validateDoctorInfo(data: DoctorInfo): void {
        const schema = createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public setDoctorInfo(data: DoctorInfo): void {
        this.validateDoctorInfo(data);
        this.doctorData.doctorInfo = data;
    }

    public getId(): string {
        return this.id;
    }
    public getAttributes(): DoctorAttributes {
        return this.doctorData;
    }
    public getDoctorInfo(): DoctorInfo {
        return this.doctorData.doctorInfo;
    }
}
