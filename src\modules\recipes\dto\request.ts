import { RecipeIngredientEnum } from "../../../enum/recipe";
import { RecipeNutrient } from "../../../entities/recipes.entity";
import { PetAllergicEnum } from "../../../enum/visit";
import { NutrientTypeEnum } from "../../../enum/requirement";

export type RecipeRequest = {
    name: string;
    ingredients: RecipeIngredientEnum[];
    nutrients: RecipeNutrient[];
};

export type NutrientMatchRequest = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
}

export type RecipeMatchRequest = {
    petNutrition: {
        nutrients: NutrientMatchRequest[];
    },
    petRequirement: {
        allergic: PetAllergicEnum[];
        foodsToAvoid: PetAllergicEnum[];
    },
}
