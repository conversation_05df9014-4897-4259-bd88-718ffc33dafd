import { RepositoryError, QueryError, SecurityError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { OwnerAttributes } from "../entities/owners.entity";
import { withMongoDB } from "../db/mongodb";
import { GetOwnerListFilterRequest, GetAllOwnerSearchRequest } from "../modules/owners/dto/request";
import { Sanitizer } from "../utils/security";
import { Db, Document } from "mongodb";
import { DATABASE_TABLE } from "../constants/database";

type OwnerFindListKeys = keyof GetOwnerListFilterRequest;
type OwnerFindListSearchObject = {
    [K in OwnerFindListKeys]: { [Key in `ownerInfo.${K}`]: { $regex: string; $options: string } };
}[OwnerFindListKeys];

type OwnerFindAllSearchObject =
    | { "ownerInfo.firstName": { $regex: string; $options: string } }
    | { "ownerInfo.lastName": { $regex: string; $options: string } }
    | { "ownerInfo.email": { $regex: string; $options: string } }
    | { "ownerInfo.phoneNumber": { $regex: string; $options: string } }
    | { $expr: { $regexMatch: { input: { $concat: string[] }; regex: string; options: string } } };

type OwnerFindListMatchFilter = {
    isDeleted: boolean;
    $and?: Array<OwnerFindListSearchObject>;
};

type OwnerFindAllMatchFilter = {
    isDeleted: boolean;
    $or?: Array<OwnerFindAllSearchObject>;
};

const DefaultFilter = {
    isDeleted: false,
};

type PipelineStage =
    | { $match: Document }
    | { $project: Document }
    | { $sort: Document }
    | { $skip: number }
    | { $limit: number };

export class OwnerRepository {
    async initCollection(db: Db) {
        return db.collection<OwnerAttributes>(DATABASE_TABLE.OWNERS);
    }
    async findOwnerById(id: string): Promise<OwnerAttributes | null> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);
                const sanitizedId = Sanitizer.sanitizeUUID(id);

                if (!Sanitizer.validateUUID(sanitizedId)) {
                    throw new SecurityError();
                }

                const response = await collection.findOne(
                    {
                        id: sanitizedId,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            if (error instanceof SecurityError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(filter?: GetAllOwnerSearchRequest): Promise<OwnerAttributes[]> {
        try {
            const ownerMatchFilter = this.buildFindAllMatchFilter(filter);

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);
                const dataPipeline = this.buildFindAllDataPipeline(ownerMatchFilter);
                const response = await collection.aggregate<OwnerAttributes>(dataPipeline).toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(
        limit: number,
        page: number,
        filter?: GetOwnerListFilterRequest
    ): Promise<{ rows: OwnerAttributes[]; count: number }> {
        try {
            const ownerMatchFilter = this.buildFindListMatchFilter(filter);

            const offset = (page - 1) * limit;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const dataPipeline = this.buildFindListDataPipeline(ownerMatchFilter, limit, offset);
                const rows = await collection.aggregate<OwnerAttributes>(dataPipeline).toArray();

                const countResult = await collection
                    .aggregate([{ $match: ownerMatchFilter }, { $count: "count" }])
                    .toArray();
                const count = countResult.length > 0 ? countResult[0].count : 0;

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findByPhoneNumber(phoneNumber: string): Promise<OwnerAttributes | null> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const response = await collection.findOne(
                    {
                        "ownerInfo.phoneNumber": escapeRegexSpecialChars(phoneNumber),
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: OwnerAttributes): Promise<OwnerAttributes> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const ownerData: OwnerAttributes = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdOwner = await collection.insertOne(ownerData);

                if (!createdOwner) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await collection.findOne({ id: ownerData.id }, { projection: { _id: 0 } });

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<OwnerAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const response = await collection.updateOne(
                    {
                        id: escapeRegexSpecialChars(id),
                        isDeleted: false,
                    },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const response = await collection.updateOne(
                    {
                        id: escapeRegexSpecialChars(id),
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    private buildFindAllDataPipeline(ownerMatchFilter: OwnerFindAllMatchFilter): PipelineStage[] {
        const pipeline: PipelineStage[] = [
            { $match: ownerMatchFilter },
            {
                $project: {
                    _id: 0,
                    id: "$id",
                    ownerInfo: "$ownerInfo",
                    createdAt: "$createdAt",
                    updatedAt: "$updatedAt",
                    isDeleted: "$isDeleted",
                    deletedAt: "$deletedAt",
                },
            },
        ];
        return pipeline;
    }

    private buildFindListDataPipeline(
        ownerMatchFilter: OwnerFindListMatchFilter,
        limit: number,
        offset: number
    ): PipelineStage[] {
        const pipeline: PipelineStage[] = [
            { $match: ownerMatchFilter },
            {
                $project: {
                    _id: 0,
                    id: "$id",
                    ownerInfo: "$ownerInfo",
                },
            },
            { $sort: { createdAt: -1 } },
            { $skip: offset },
            { $limit: limit },
        ];
        return pipeline;
    }

    private buildFindListMatchFilter(filter?: GetOwnerListFilterRequest): OwnerFindListMatchFilter {
        const ownerMatchFilter: OwnerFindListMatchFilter = { ...DefaultFilter };
        if (filter) {
            const { firstName, lastName, phoneNumber, email } = filter;

            const ownerAndConditions: OwnerFindListSearchObject[] = [];
            if (firstName)
                ownerAndConditions.push({
                    "ownerInfo.firstName": { $regex: escapeRegexSpecialChars(firstName), $options: "i" },
                });
            if (lastName)
                ownerAndConditions.push({
                    "ownerInfo.lastName": { $regex: escapeRegexSpecialChars(lastName), $options: "i" },
                });
            if (phoneNumber)
                ownerAndConditions.push({
                    "ownerInfo.phoneNumber": { $regex: escapeRegexSpecialChars(phoneNumber), $options: "i" },
                });
            if (email)
                ownerAndConditions.push({
                    "ownerInfo.email": { $regex: escapeRegexSpecialChars(email), $options: "i" },
                });

            if (ownerAndConditions.length > 0) ownerMatchFilter.$and = ownerAndConditions;
        }
        return ownerMatchFilter;
    }

    private buildFindAllMatchFilter(filter?: GetAllOwnerSearchRequest): OwnerFindAllMatchFilter {
        const ownerMatchFilter: OwnerFindAllMatchFilter = { ...DefaultFilter };
        if (filter) {
            const { text } = filter;
            if (text) {
                const escapedText = escapeRegexSpecialChars(text);
                const ownerOrConditions: OwnerFindAllSearchObject[] = [
                    { "ownerInfo.firstName": { $regex: escapedText, $options: "i" } },
                    { "ownerInfo.lastName": { $regex: escapedText, $options: "i" } },
                    { "ownerInfo.email": { $regex: escapedText, $options: "i" } },
                    { "ownerInfo.phoneNumber": { $regex: escapedText, $options: "i" } },
                    {
                        $expr: {
                            $regexMatch: {
                                input: {
                                    $concat: ["$ownerInfo.firstName", " ", "$ownerInfo.lastName"],
                                },
                                regex: escapedText,
                                options: "i",
                            },
                        },
                    },
                ];
                ownerMatchFilter.$or = ownerOrConditions;
            }
        }
        return ownerMatchFilter;
    }
}

export default new OwnerRepository();
