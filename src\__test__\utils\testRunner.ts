interface MethodCalls {
    method: jest.Mock;
    called: boolean;
    withArgs?: any[];
}

interface ErrorFormat {
    errorType?: string;
    errorTitle?: string;
    message: string;
    data?: any;
    error?: any;
    success?: boolean;
}

interface ExpectedError {
    instance: new (...args: any[]) => Error;
    properties: ErrorFormat;
}

/**
 * TestCase Interface
 *
 * A generic interface for defining test cases with type-safe inputs and outputs.
 *
 * @template I - The input type for the test case
 * @template R - The expected return type of the method being tested
 *
 * @property {string} name - A descriptive name for the test case
 *
 * @property {Object} setup - Configuration for test setup
 *   @property {I} input - The input data for the test
 *   @property {(() => void) | null} mocks - [Optional] function to set up mocks
 *   @property {() => void} teardown - [Optional] cleanup function to run after test
 *
 * @property {(input: I) => Promise<R>} method - The async function to test
 *
 * @property {Object} expectation - [Optional] expected outcomes and validations
 *   @property {R | null} result - [Optional] expected successful result
 *   @property {ExpectedError | null} error - [Optional] expected error if test should fail
 *   @property {MethodCalls[]} methodCalls - [Optional] expected method calls and their arguments
 *   @property {Object} additionalChecks - [Optional] additional validation functions
 *     @property {(result: R) => void} onSuccess - [Optional] function to run on successful test
 *     @property {(input: I) => Promise<void>} onError - [Optional] function to run when test fails
 */
export interface TestCase<I, R> {
    name: string;
    setup: { input: I; mocks?: (() => void) | null; teardown?: () => void };
    method: (input: I) => Promise<R>;
    expectation: {
        result?: R | null;
        error?: ExpectedError | null;
        methodCalls?: MethodCalls[];
        additionalChecks?: {
            onSuccess?: (result: R) => void;
            onError?: (input: I) => Promise<void>;
        } | null;
    };
}
/**
 * Executes a test case for a module method with the following steps:
 *
 * @template I - Type of the input data for the test case
 * @template R - Type of the expected result from the test case
 *
 * @param testCase - The test case configuration containing:
 *   - name: Test case description
 *   - setup: Test environment setup including input data and mocks
 *   - method: The actual method to test
 *   - expectation: Expected results, errors, and repository calls
 *
 * @returns An async function that can be used with Jest's test/it functions
 */
export function runTestCase<I, R>(testCase: TestCase<I, R>) {
    console.error = () => {};
    console.log = () => {};
    console.warn = () => {};
    console.info = () => {};
    console.debug = () => {};
    return async () => {
        const { input, mocks, teardown } = testCase.setup;
        const { result, error, methodCalls, additionalChecks } = testCase.expectation;

        mocks?.();

        const method = testCase.method;

        if (result || result === undefined) {
            const successResult = await method(input);
            expect(successResult).toEqual(result);
            additionalChecks?.onSuccess?.(successResult);
        } else if (error) {
            const errorResult = method(input);
            await expect(errorResult).rejects.toBeInstanceOf(error.instance);
            await expect(errorResult).rejects.toMatchObject(error.properties);
            additionalChecks?.onError?.(input);
        }

        methodCalls?.forEach((call) => {
            const { method, called, withArgs } = call;
            if (called) {
                if (withArgs) expect(method).toHaveBeenCalledWith(...withArgs);
                else expect(method).toHaveBeenCalled();
            } else {
                expect(method).not.toHaveBeenCalled();
            }
        });

        teardown?.();
    };
}
