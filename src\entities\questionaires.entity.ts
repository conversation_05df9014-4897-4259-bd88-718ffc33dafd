import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig } from "../validations/questionaires/questionaires.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { PetSpeciesEnum } from "../enum/pet";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";

type QuestionOption = {
    index: number;
    text: string;
    score: number;
    isSelected: boolean;
};

export type Questionaires = {
    title: string;
    section: string;
    order: number;
    questions: QuestionOption[];
};

export type QuestionaireAttributes = {
    id: string;
    type: string;
    species: PetSpeciesEnum;
    questionaires: Questionaires[];
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export class QuestionaireEntity {
    private readonly id: string | null;
    private readonly questionaireData: QuestionaireAttributes;
    constructor(
        id: string | null = null, 
        preData: QuestionaireAttributes | null = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.questionaireData = this.initialize(preData, customConfig);
    }

    private initialize(preData: QuestionaireAttributes | null, customConfig?: Record<string, FieldConfigType>
    ): QuestionaireAttributes {
        try {
            this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                type: preData?.type,
                species: preData?.species,
                questionaires: preData?.questionaires,
            } as QuestionaireAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: QuestionaireAttributes | null, customConfig?: Record<string, FieldConfigType>
    ): void {
        if (preData) this.validateQuestionaireData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateQuestionaireData(data: QuestionaireAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.questionaireData.id;
    }

    public getAttributes(): QuestionaireAttributes {
        return this.questionaireData;
    }
}
