import { PetSpeciesEnum } from "../../../enum/pet";
import { NutrientTypeEnum } from "../../../enum/requirement";

type NutrientDetailRange = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
};

export type RequirementResponse = {
    id?: string;
    name: string;
    species: PetSpeciesEnum;
    nutrients: NutrientDetailRange[];
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type RequirementNameResponse = {
    name: string;
}

export type RequirementListResponse = {
    rows: RequirementResponse[];
    count: number;
};
