import { ServiceError } from "../middlewares/errorHandler";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import https from "https";
import fetch, { Headers, Response } from "node-fetch";
import jwt from "jsonwebtoken";
import { CredentialsTypeEnum } from "../enum/user";
import { UserFilterRequest } from "../modules/users/dto/request";

interface RequestParams {
    [key: string]: string | number | boolean;
}

export type ProviderUserAttributes = {
    id: string;
    username: string;
    firstName: string;
    lastName: string;
    email: string;
    emailVerified: boolean;
    createdTimestamp: number;
    enabled: boolean;
};

type ClientAttribute = {
    id: string;
    clientId: string;
    name: string;
    description?: string;
};

export type RealmRoleAttribute = {
    id: string;
    name: string;
    description?: string;
    composite: boolean;
    clientRole: boolean;
    containerId: string;
};

export type ClientRoleAttribute = {
    id: string;
    name: string;
    description?: string;
    composite?: boolean;
    clientRole?: boolean;
    containerId?: string;
};

export type ClientRoleRepresentation = {
    name: string;
    description?: string;
};

export type CredentialRepresentation = {
    type: string;
    value: string;
    temporary: boolean;
};

export type UserRepresentation = {
    username: string;
    firstName?: string;
    lastName?: string;
    email?: string;
    emailVerified?: boolean;
    enabled?: boolean;
    credentials?: CredentialRepresentation[];
    realmRoles?: string[];
    clientRoles?: ClientRolesMapAttribute;
    groups?: string[];
};

export type GroupAttribute = {
    id: string;
    name: string;
};

export type GroupRepresentation = {
    name: string;
    path?: string;
    realmRoles?: string[];
    clientRoles?: ClientRolesMapAttribute;
    subGroups?: GroupAttribute[];
};

export type ClientRolesMapAttribute = Map<ClientId, Role[]>;

type ClientId = string; // Client identifiers such as 'admin-client', 'user-client'.
type Role = string; // The name of the role, such as 'manage-users', 'edit-settings'.

interface KeyFormat {
    kid: string;
    kty: string;
    alg: string;
    use: string;
    n: string;
    e: string;
}

type ProviderUserFilter = {
    username?: string;
    firstName?: string;
    lastName?: string;
    email?: string;
};

const enableSSL = process.env.ENABLE_SSL === "true";

export class KeycloakService {
    private url!: string;
    private realm!: string;
    private clientId!: string;
    private clientUUid!: string;
    private authToken: string | null = null;

    constructor(token: string) {
        this.authToken = token ?? null;
        this.initialize();
    }
    private initialize() {
        const authUrl = process.env.KEYCLOAK_SERVER_URL;
        const authRealm = process.env.KEYCLOAK_REALM;
        const authClientId = process.env.KEYCLOAK_CLIENT_ID;
        const authClientUuid = process.env.KEYCLOAK_CLIENT_UUID;

        if (!authUrl || !authRealm || !authClientId || !authClientUuid) {
            throw new ServiceError(s.SERVICE_UNAVAILABLE, m.SERVICE_CONFIGURED_ERROR);
        }
        this.url = authUrl;
        this.realm = authRealm;
        this.clientId = authClientId;
        this.clientUUid = authClientUuid;
    }

    private buildUrl(endpoint: string, params?: RequestParams): string {
        const url = new URL(`${this.url}${endpoint}`);
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                url.searchParams.append(key, value.toString());
            });
        }
        return url.toString();
    }

    private buildHeaders(customHeaders: fetch.HeaderInit = {}): Headers {
        const headers = new Headers(customHeaders);
        if (!headers.get("Content-Type")) headers.set("Content-Type", "application/json");
        if (this.authToken) headers.set("Authorization", `Bearer ${this.authToken}`);
        return headers;
    }

    private async _fetch(
        endpoint: string,
        method: string,
        body?: string,
        params?: RequestParams,
        customHeaders: fetch.HeaderInit = {},
        agent?: https.Agent
    ): Promise<any> {
        agent ??= enableSSL ? new https.Agent({ rejectUnauthorized: false }) : undefined;

        const url = this.buildUrl(endpoint, params);
        const headers = this.buildHeaders(customHeaders);

        try {
            const response = await fetch(url, { method, headers, body, agent });

            if (!response.ok) {
                throw await this.mapKeycloakErrorToDefaultError(response);
            }
            const isJson = await this.isJsonResponse(response);
            return isJson ? await response.json() : { message: "SUCCESS" };
        } catch (error) {
            if (error instanceof ServiceError) throw error;
            throw new ServiceError(s.INTERNAL_SERVER_ERROR, m.INTERNAL_SERVER_ERROR);
        }
    }

    private async isJsonResponse(response: Response): Promise<boolean> {
        const contentType = response.headers.get("content-type");
        return Boolean(contentType?.includes("application/json"));
    }

    public setAuthToken(token: string) {
        this.authToken = token;
    }

    private readonly mapKeycloakErrorToDefaultError = async (
        response: Response
    ): Promise<ServiceError<object | null>> => {
        const body = await response.json();
        switch (response.status) {
            case 400:
                return new ServiceError(s.BAD_REQUEST, m.BAD_REQUEST);
            case 401:
                return new ServiceError(s.UNAUTHENTICATED, m.UNAUTHENTICATED);
            case 403:
                return new ServiceError(s.FORBIDDEN, m.FORBIDDEN);
            case 404:
                return new ServiceError(s.NOT_FOUND, m.NOT_FOUND);
            case 409:
                return new ServiceError(s.CONFLICT, m.CONFLICT, body as {} | null);
            case 429:
                return new ServiceError(s.TOO_MANY_REQUESTS, m.TOO_MANY_REQUESTS);
            case 500:
            case 502:
            case 503:
            case 504:
                return new ServiceError(response.status, m.SERVICE_UNAVAILABLE);
            default:
                return new ServiceError(response.status, m.INTERNAL_SERVER_ERROR);
        }
    };

    public async tokenDecoder(token: string) {
        try {
            const key = await this.getKey();
            const algorithms = process.env.ALGORITHM! as jwt.Algorithm;
            const decodedToken = jwt.verify(token, key, { algorithms: [algorithms] });
            return decodedToken;
        } catch (err) {
            if (err instanceof jwt.TokenExpiredError) {
                throw new ServiceError(s.UNAUTHENTICATED, m.TOKEN_EXPIRED_ERROR);
            }
            if (err instanceof jwt.JsonWebTokenError) {
                throw new ServiceError(s.UNAUTHENTICATED, m.INVALID_TOKEN);
            }
            if (err instanceof ServiceError) {
                throw err;
            }
            throw new ServiceError(s.INTERNAL_SERVER_ERROR, m.DECODE_TOKEN_ERROR);
        }
    }

    private async getKey(): Promise<string> {
        const agent = new https.Agent({ rejectUnauthorized: false });
        const publicKeyResponse = await this._fetch(
            `/realms/${this.realm}/protocol/openid-connect/certs`,
            "GET",
            undefined,
            undefined,
            undefined,
            agent
        );
        const publicKeys = publicKeyResponse.keys;
        const algorithms = process.env.ALGORITHM! as jwt.Algorithm;
        const publicKey = publicKeys.find(
            (key: KeyFormat) => key.use === "sig" && key.kty === "RSA" && key.alg === algorithms
        );
        if (!publicKey) throw new ServiceError(s.INTERNAL_SERVER_ERROR, m.KEY_NOT_FOUND);
        const pemKey = `-----BEGIN CERTIFICATE-----\n${publicKey.x5c[0]}\n-----END CERTIFICATE-----`;
        return pemKey;
    }

    public extractRoles(decoded: jwt.JwtPayload): string[] {
        if (this.clientId && decoded.resource_access?.[this.clientId]?.roles) {
            return decoded.resource_access[this.clientId].roles;
        }
        return [];
    }

    //users
    public async getAllUsers(): Promise<ProviderUserAttributes[]> {
        const data = await this._fetch(`/admin/realms/${this.realm}/users`, "GET");
        return data;
    }
    public async getUserById(id: string): Promise<ProviderUserAttributes> {
        const data = await this._fetch(`/admin/realms/${this.realm}/users/${id}`, "GET");
        return data;
    }
    public async searchUser(userFilter?: ProviderUserFilter): Promise<ProviderUserAttributes[]> {
        let filterParams: UserFilterRequest | undefined = {};
        if (userFilter?.username) filterParams.username = userFilter.username;
        if (userFilter?.firstName) filterParams.firstName = userFilter.firstName;
        if (userFilter?.lastName) filterParams.lastName = userFilter.lastName;
        if (userFilter?.email) filterParams.email = userFilter.email;

        const data = await this._fetch(`/admin/realms/${this.realm}/users`, "GET", undefined, filterParams);
        return data;
    }
    public async createUser(userRepresentation: UserRepresentation): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/users`, "POST", JSON.stringify(userRepresentation));
    }
    public generateUserCredential(): CredentialRepresentation {
        return {
            type: CredentialsTypeEnum.PASSWORD,
            value: this.generatePassword(),
            temporary: true,
        };
    }
    private generatePassword(): string {
        const characters = process.env.CHARACTERS!;
        let password = "";
        for (let i = 0; i < 8; i++) {
            const randomIndex = Math.floor(Math.random() * characters.length);
            password += characters.charAt(randomIndex);
        }
        return password;
    }
    public async updateUser(id: string, userRepresentation: UserRepresentation): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/users/${id}`, "PUT", JSON.stringify(userRepresentation));
    }
    public async deleteUser(id: string): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/users/${id}`, "DELETE");
    }
    public async getUserGroups(id: string): Promise<GroupAttribute[]> {
        const data = await this._fetch(`/admin/realms/${this.realm}/users/${id}/groups`, "GET");
        return data;
    }

    //groups
    public async getGroups(): Promise<GroupAttribute[]> {
        const data = await this._fetch(`/admin/realms/${this.realm}/groups`, "GET");
        return data;
    }
    public async getGroupById(id: string): Promise<GroupAttribute> {
        const data = await this._fetch(`/admin/realms/${this.realm}/groups/${id}`, "GET");
        return data;
    }
    public async createGroup(groupRepresentation: GroupRepresentation): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/groups`, "POST", JSON.stringify(groupRepresentation));
    }
    public async updateGroup(id: string, groupRepresentation: GroupRepresentation): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/groups/${id}`, "PUT", JSON.stringify(groupRepresentation));
    }
    public async deleteGroup(id: string): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/groups/${id}`, "DELETE");
    }

    //clients
    public async getClientByClientId(clientId: string = this.clientId): Promise<ClientAttribute[]> {
        const params = { clientId };
        const data = await this._fetch(`/admin/realms/${this.realm}/clients`, "GET", undefined, params);
        return data;
    }
    //client roles
    public async getClientRoles(): Promise<ClientRoleAttribute[]> {
        const data = await this._fetch(`/admin/realms/${this.realm}/clients/${this.clientUUid}/roles`, "GET");
        return data;
    }
    public async getClientRoleByName(roleName: string): Promise<ClientRoleAttribute> {
        const data = await this._fetch(
            `/admin/realms/${this.realm}/clients/${this.clientUUid}/roles/${roleName}`,
            "GET"
        );
        return data;
    }
    public async createClientRole(roleRepresentation: ClientRoleRepresentation): Promise<void> {
        await this._fetch(
            `/admin/realms/${this.realm}/clients/${this.clientUUid}/roles`,
            "POST",
            JSON.stringify(roleRepresentation)
        );
    }
    public async updateClientRole(roleName: string, roleRepresentation: ClientRoleRepresentation): Promise<void> {
        await this._fetch(
            `/admin/realms/${this.realm}/clients/${this.clientUUid}/roles/${roleName}`,
            "PUT",
            JSON.stringify(roleRepresentation)
        );
    }
    public async deleteClientRole(roleName: string): Promise<void> {
        await this._fetch(`/admin/realms/${this.realm}/clients/${this.clientUUid}/roles/${roleName}`, "DELETE");
    }
    //role mapper
    //User
    public async getClientRoleMappingsForUser(userId: string): Promise<ClientRoleAttribute[]> {
        const data = await this._fetch(
            `/admin/realms/${this.realm}/users/${userId}/role-mappings/clients/${this.clientUUid}`,
            "GET"
        );
        return data;
    }
    public async addClientRoleToUser(userId: string, RoleRepresentation: ClientRoleAttribute[]): Promise<void> {
        await this._fetch(
            `/admin/realms/${this.realm}/users/${userId}/role-mappings/clients/${this.clientUUid}`,
            "POST",
            JSON.stringify(RoleRepresentation)
        );
    }
    public async removeClientRoleFromUser(userId: string, RoleRepresentation: ClientRoleAttribute[]): Promise<void> {
        await this._fetch(
            `/admin/realms/${this.realm}/users/${userId}/role-mappings/clients/${this.clientUUid}`,
            "DELETE",
            JSON.stringify(RoleRepresentation)
        );
    }
    //group
    public async getClientRoleMappingsForGroup(groupId: string): Promise<ClientRoleAttribute[]> {
        const data = await this._fetch(
            `/admin/realms/${this.realm}/groups/${groupId}/role-mappings/clients/${this.clientUUid}`,
            "GET"
        );
        return data;
    }
    public async addClientRoleToGroup(groupId: string, RoleRepresentation: ClientRoleAttribute[]): Promise<void> {
        await this._fetch(
            `/admin/realms/${this.realm}/groups/${groupId}/role-mappings/clients/${this.clientUUid}`,
            "POST",
            JSON.stringify(RoleRepresentation)
        );
    }
    public async removeClientRoleFromGroup(groupId: string, RoleRepresentation: ClientRoleAttribute[]): Promise<void> {
        await this._fetch(
            `/admin/realms/${this.realm}/groups/${groupId}/role-mappings/clients/${this.clientUUid}`,
            "DELETE",
            JSON.stringify(RoleRepresentation)
        );
    }
}
