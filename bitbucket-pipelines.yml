image: google/cloud-sdk:latest

definitions:
    caches:
        docker-cliplugins: ~/.docker/cli-plugins

    steps:
        - step: &install
              name: Install and Test
              image: node:22
              caches:
                  - node
              script:
                  - npm install --ignore-scripts
                  - npm run build
                  - npm run test:jest

        - step: &addEnvVariables
              name: Add Environment File
              script:
                  - echo "This step add environment files"
                  - |
                      cat << EOF > .env
                            NODE_ENV=${NODE_ENV}
                            PORT=${PORT}
                            MONGO_URI=mongodb://${MONGODB_USERNAME}:${MONGODB_PASSWORD}@${MONGODB_HOST}:${MONGODB_PORT}/${MONGODB_DATABASE_NAME}?authSource=admin
                            DB_NAME=${MONGODB_DATABASE_NAME}
                            APP_NAME=${APP_NAME}
                            CHARACTERS=${CHARACTERS}
                            ALGORITHM=${ALGORITHM}
                            KEYCLOAK_SERVER_URL=${KEYCLOAK_SERVER_URL}
                            KEYCLOAK_REALM=${KEYCLOAK_REALM}
                            KEYCLOAK_CLIENT_ID=${KEYCLOAK_CLIENT_ID}
                            KEYCLOAK_CLIENT_UUID=${KEYCLOAK_CLIENT_UUID}
                      EOF
              artifacts:
                  - ".env"

        - step: &buildImagePushToRegistry
              name: Build Image to Registry
              services: [docker]
              oidc: true
              max-time: 60
              script:
                  - echo "$BITBUCKET_STEP_OIDC_TOKEN" > ./oidc_token
                  - gcloud iam workload-identity-pools create-cred-config ${GCP_WORKLOAD_IDENTITY} --service-account=${GCP_SERVICE_ACCOUNT} --output-file=./gcloud_cred_config.json --credential-source-file=./oidc_token --credential-source-type=text
                  - gcloud auth login --cred-file=./gcloud_cred_config.json
                  - gcloud auth configure-docker $GCP_REGION-docker.pkg.dev
                  - export DOCKER_BUILDKIT=0
                  - export IMAGE_TAG=${NODE_ENV}:latest
                  - export IMAGE_NAME=$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$REPOSITORY_NAME/$IMAGE_TAG
                  - echo "$IMAGE_TAG"
                  - echo "$IMAGE_NAME"
                  - docker build -f src/docker/api-service/Dockerfile -t $IMAGE_TAG .
                  - docker tag $IMAGE_TAG $IMAGE_NAME
                  - docker push $IMAGE_NAME
              artifacts:
                  - "gcloud_cred_config.json"
                  - "oidc_token"

        - step: &deployImageToVM
              name: Deploy Image to VM
              script:
                  - export DOCKER_BUILDKIT=0
                  - export IMAGE_TAG=${NODE_ENV}:latest
                  - export IMAGE_NAME=$GCP_REGION-docker.pkg.dev/$GCP_PROJECT_ID/$REPOSITORY_NAME/$IMAGE_TAG
                  - echo "$IMAGE_TAG"
                  - echo "$IMAGE_NAME"
                  - pipe: atlassian/scp-deploy:0.3.3
                    variables:
                        USER: $SSH_GCP_USER
                        SERVER: $SSH_GCP_SERVER
                        REMOTE_PATH: "./"
                        LOCAL_PATH: "gcloud_cred_config.json"
                  - pipe: atlassian/scp-deploy:0.3.3
                    variables:
                        USER: $SSH_GCP_USER
                        SERVER: $SSH_GCP_SERVER
                        REMOTE_PATH: "./"
                        LOCAL_PATH: "oidc_token"
                  - pipe: atlassian/scp-deploy:0.3.3
                    variables:
                        USER: $SSH_GCP_USER
                        SERVER: $SSH_GCP_SERVER
                        REMOTE_PATH: "./"
                        LOCAL_PATH: ".env"
                  - pipe: atlassian/ssh-run:0.8.0
                    variables:
                        SSH_USER: $SSH_GCP_USER
                        SERVER: $SSH_GCP_SERVER
                        COMMAND: >
                            ls ;
                            gcloud auth activate-service-account --key-file=./gcloud_cred_config.json --quiet ;
                            gcloud auth configure-docker $GCP_REGION-docker.pkg.dev --quiet ;
                            docker pull $IMAGE_NAME ;
                            docker stop $APP_NAME || true ;
                            docker rm $APP_NAME || true ;
                            docker run -d --name $APP_NAME --env-file .env -p $API_PORT --network nginx_proxy $IMAGE_NAME ;
              artifacts:
                  - "gcloud_cred_config.json"
                  - "oidc_token"

        - step: &removeUnusedImageOnVM
              name: Remove unused image on VM
              script:
                  - echo "Remove Unused Image:"
                  - pipe: atlassian/ssh-run:0.8.0
                    variables:
                        SSH_USER: $SSH_GCP_USER
                        SERVER: $SSH_GCP_SERVER
                        COMMAND: >
                            docker image prune --force --filter "dangling=true"

pipelines:
    pull-requests:
        "**":
            - step: *install
    branches:
        "{develop}":
            - stage:
                  name: CD Develop
                  deployment: Develop
                  steps:
                      - step: *addEnvVariables
                      - step: *buildImagePushToRegistry
                      - step: *deployImageToVM
                      - step: *removeUnusedImageOnVM
        "{sit}":
            - stage:
                  name: CD SIT
                  deployment: SIT
                  steps:
                      - step: *addEnvVariables
                      - step: *buildImagePushToRegistry
                      - step: *deployImageToVM
                      - step: *removeUnusedImageOnVM

options:
    max-parallel:
        default: 1
