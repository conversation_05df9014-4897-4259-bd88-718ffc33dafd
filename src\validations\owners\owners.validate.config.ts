import { FieldConfigMap } from "../../types/validate.type";
import { OwnerValidateType, OwnerFilterValidateType, OwnerGetAllFilterValidateType } from "./owners.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";

export const createConfig: FieldConfigMap<OwnerValidateType> = {
    ownerInfo: { type: v.OWNER_INFO, required: true },
};

export const filterConfig: FieldConfigMap<OwnerFilterValidateType> = {
    firstName: { type: v.LETTER_ONLY, required: false, minLength: 1, maxLength: 255 },
    lastName: { type: v.LETTER_ONLY, required: false, minLength: 1, maxLength: 255 },
    phoneNumber: { type: v.PHONE, required: false },
    email: { type: v.EMAIL, required: false },
};
export const getAllFilterConfig: FieldConfigMap<OwnerGetAllFilterValidateType> = {
    text: { type: v.STRING, required: false, minLength: 1, maxLength: 255 },
};
