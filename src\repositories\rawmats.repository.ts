import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { RawMatAttributes } from "../entities/rawmats.entity";
import { withMongoDB } from "../db/mongodb";
import { Db } from "mongodb";
import { DATABASE_TABLE } from "../constants/database";

type Filter = {
    isDeleted: boolean;
    name?: string;
};
const DefaultFilter: Filter = {
    isDeleted: false,
};
export class RawmatRepository {
    async initCollection(db: Db) {
        return db.collection<RawMatAttributes>(DATABASE_TABLE.RAWMATS);
    }
    async getAll(): Promise<RawMatAttributes[]> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);
                const rawmats = await collection.find(DefaultFilter).toArray();
                return rawmats;
            });
        } catch (error) {
            console.error(error);
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async create(data: RawMatAttributes): Promise<RawMatAttributes> {
        try {
            const rawmatData: RawMatAttributes = {
                ...data,
                isDeleted: false,
                createdAt: new Date(),
                updatedAt: new Date(),
                deletedAt: null,
            };
            const createdRawmat = await withMongoDB(async (db) => {
                const collection = await this.initCollection(db);
                return collection.insertOne(rawmatData);
            });
            if (!createdRawmat) {
                throw new QueryError(m.INSERTION_FAILED);
            }

            return rawmatData;
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
    async findByName(name: string): Promise<RawMatAttributes | null> {
        try {
            const filter: Filter = { ...DefaultFilter };
            if (name) filter.name = name;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);
                const rawmat = await collection.findOne(filter);
                return rawmat;
            });
        } catch (error) {
            console.error(error);
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}
