import {
    PetGenderTypeEnum,
    PetSpeciesEnum,
    PetLivingEnvironmentEnum,
    ActiveScoreCalculationMethodEnum,
    ActiveScoreVisualLevelEnum,
} from "../enum/pet";
import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig, filterConfig } from "../validations/pets/pets.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { bcsConstants } from "../constants/pet";
import { Questionaires } from "./questionaires.entity";
import { PetRequest, GetPetListFilterRequest } from "../modules/pets/dto/request";

export type PetWeights = {
    weight: number;
    measuredDate: Date;
};

export type ActiveScore = {
    score: number;
    calculationMethod: ActiveScoreCalculationMethodEnum;
    questionaires: Questionaires[] | null;
    visualLevel: ActiveScoreVisualLevelEnum | null;
};

export type Diseases = {
    diseaseId: string;
    diseaseName: string;
    validFrom: Date;
    validTo: Date;
    createdAt: Date;
    updatedAt: Date;
};

export type PetInfo = {
    hn: string;
    name: string;
    gender: PetGenderTypeEnum;
    species: PetSpeciesEnum;
    breed: string;
    isMixedBreed: boolean;
    birthDate: Date | null;
    weights: PetWeights[];
    livingEnvironment: PetLivingEnvironmentEnum;
    isNeutering: boolean;
    diseases: Diseases[];
    activeScore: ActiveScore;
};

export type PetAttributes = {
    id: string;
    ownerId: string;
    petInfo: PetInfo;
    isDeleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date | null;
};

export type PetInputData = PetRequest | PetAttributes | null;

export class PetEntity {
    private readonly id: string | null;
    private readonly petData: PetAttributes;
    constructor(
        id: string | null = null,
        preData: PetInputData = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.petData = this.initialize(preData, customConfig);
    }

    private initialize(preData: PetInputData, customConfig?: Record<string, FieldConfigType>): PetAttributes {
        try {
            const validatedData = this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                ownerId: validatedData?.ownerId,
                petInfo: validatedData?.petInfo,
            } as PetAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(
        preData: PetInputData,
        customConfig?: Record<string, FieldConfigType>
    ): PetAttributes | null {
        let validatedData: PetAttributes | null = null;
        if (preData) validatedData = this.validatePetData(preData, customConfig);
        if (this.id) this.validateId(this.id);
        return validatedData;
    }

    private validatePetData(data: PetAttributes | PetRequest, customConfig?: Record<string, FieldConfigType>): PetAttributes {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
        return result.data as PetAttributes;
    }

    public validateFilter(filter: GetPetListFilterRequest): void {
        const result = validated(filterConfig, filter);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.petData.id;
    }

    public getAttributes(): PetAttributes {
        return this.petData;
    }

    public calculateBCSScore(scores: number[]): number {
        if (!scores || scores.length === 0) {
            throw new EntityError(m.INVALID_BCS_SCORE_FORMAT, null);
        }

        const average = scores.reduce((acc, score) => acc + score, 0) / scores.length;
        const bcsScore =
            parseInt((average * bcsConstants.RANGE_INDEX).toFixed(bcsConstants.DECIMAL_PLACES)) +
            bcsConstants.INDEX_TO_BCS_VALUE;

        return bcsScore;
    }
}
