import { FieldConfigMap } from "../../types/validate.type";
import { QuestionaireValidateType } from "./questionaires.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { PetSpeciesEnum } from "../../enum/pet";

export const createConfig: FieldConfigMap<QuestionaireValidateType> = {
    type: {
        type: v.STRING,
        required: true,
    },
    species: {
        type: v.ENUM,
        required: true,
        enumValues: Object.values(PetSpeciesEnum),
    },
    questionaires: {
        type: v.QUESTIONARE,
        required: true,
    },
};
