import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { RawmatModule } from "../modules/rawmats/rawmat.module";
import { RawmatRequest } from "../modules/rawmats/dto/request";

export class RawmatController {
    private readonly rawmatModule: RawmatModule;
    public router: Router;
    constructor(rawmatModule: RawmatModule) {
        this.rawmatModule = rawmatModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/all", this.getAll.bind(this));
        this.router.post("/", this.create.bind(this));
    }
    private async getAll(_: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.rawmatModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as RawmatRequest;
            const response = await this.rawmatModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
