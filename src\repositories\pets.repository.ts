import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { PetAttributes } from "../entities/pets.entity";
import { withMongoDB } from "../db/mongodb";
import { Db, Document } from "mongodb";
import { DATABASE_TABLE } from "../constants/database";
import { GetPetListFilterRequest } from "../modules/pets/dto/request";
import { PetWithOwnerResponse, PetWithOwnerListResponse } from "../modules/pets/dto/response";

type PetNameSearchObject = { "petInfo.name": { $regex: string; $options: string } };

type PetHnSearchObject = { "petInfo.hn": string };

type PetMatchFilter = {
    isDeleted: boolean;
    $and?: Array<PetHnSearchObject | PetNameSearchObject>;
};

type OwnerNameSearchObject =
    | { "ownerData.ownerInfo.firstName": { $regex: string; $options: string } }
    | { "ownerData.ownerInfo.lastName": { $regex: string; $options: string } }
    | { $expr: { $regexMatch: { input: { $concat: string[] }; regex: string; options: string } } };

type OwnerPhoneSearchObject = { "ownerData.ownerInfo.phoneNumber": string };

type OwnerMatchFilter = {
    "ownerData.isDeleted": boolean;
    $or?: Array<OwnerNameSearchObject>;
    $and?: Array<OwnerPhoneSearchObject>;
};

const DefaultPetFilter = {
    isDeleted: false,
};

const DefaultOwnerFilter: OwnerMatchFilter = {
    "ownerData.isDeleted": false,
};

type PipelineStage = 
    | { $match: Document }
    | { $lookup: { from: string; localField: string; foreignField: string; as: string } }
    | { $unwind: string }
    | { $project: Document }
    | { $sort: Document }
    | { $skip: number }
    | { $limit: number }
    | { $count: string };

export class PetRepository {
    async initCollection(db: Db) {
        return db.collection<PetAttributes>(DATABASE_TABLE.PETS);
    }

    async findPetById(id: string): Promise<PetAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<PetAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const response = await db
                    .collection<PetAttributes>("pets")
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .sort({ "petInfo.hn": 1 })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(limit: number, page: number, filter?: GetPetListFilterRequest): Promise<PetWithOwnerListResponse> {
        try {
            const petMatchFilter = this.buildPetMatchFilter(filter);
            const ownerMatchFilter = this.buildOwnerMatchFilter(filter);
            const offset = (page - 1) * limit;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const dataPipeline = this.buildFindListDataPipeline(petMatchFilter, ownerMatchFilter, limit, offset);
                const countPipeline = this.buildFindListCountPipeline(petMatchFilter, ownerMatchFilter);

                const [rows, countResult] = await Promise.all([
                    collection.aggregate<PetWithOwnerResponse>(dataPipeline).toArray(),
                    collection.aggregate(countPipeline).toArray(),
                ]);

                const count = countResult.length > 0 ? countResult[0].count : 0;

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findByHN(hn: string): Promise<PetAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").findOne(
                    {
                        "petInfo.hn": hn,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: PetAttributes): Promise<PetAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const petData = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdPet = await db.collection<PetAttributes>("pets").insertOne(petData);

                if (!createdPet) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await db
                    .collection<PetAttributes>("pets")
                    .findOne({ id: petData.id }, { projection: { _id: 0 } });

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<PetAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<PetAttributes>("pets").updateOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    private buildPetMatchFilter(filter?: GetPetListFilterRequest): PetMatchFilter {
        const petMatchFilter: PetMatchFilter = { ...DefaultPetFilter };

        if (filter) {
            const { petHnId, petName } = filter;
            const petAndConditions: (PetHnSearchObject | PetNameSearchObject)[] = [];

            if (petHnId) petAndConditions.push({ "petInfo.hn": petHnId });
            if (petName) petAndConditions.push({ "petInfo.name": { $regex: `^${petName}`, $options: "i" } });

            if (petAndConditions.length > 0) petMatchFilter.$and = petAndConditions;
        }

        return petMatchFilter;
    }

    private buildOwnerMatchFilter(filter?: GetPetListFilterRequest): OwnerMatchFilter {
        const ownerMatchFilter: OwnerMatchFilter = { ...DefaultOwnerFilter };

        if (filter) {
            const { ownerName, ownerPhoneNumber } = filter;

            if (ownerName) {
                const ownerOrConditions: OwnerNameSearchObject[] = [
                    {
                        "ownerData.ownerInfo.firstName": {
                            $regex: ownerName,
                            $options: "i",
                        },
                    },
                    {
                        "ownerData.ownerInfo.lastName": {
                            $regex: ownerName,
                            $options: "i",
                        },
                    },
                    {
                        $expr: {
                            $regexMatch: {
                                input: {
                                    $concat: ["$ownerData.ownerInfo.firstName", " ", "$ownerData.ownerInfo.lastName"],
                                },
                                regex: ownerName,
                                options: "i",
                            },
                        },
                    },
                ];
                ownerMatchFilter.$or = ownerOrConditions;
            }

            if (ownerPhoneNumber) {
                ownerMatchFilter.$and = [{ "ownerData.ownerInfo.phoneNumber": ownerPhoneNumber }];
            }
        }

        return ownerMatchFilter;
    }

    private buildFindListBasePipeline(petMatchFilter: PetMatchFilter, ownerMatchFilter: OwnerMatchFilter): PipelineStage[] {
        const pipeline: PipelineStage[] = [
            { $match: petMatchFilter },
            {
                $lookup: {
                    from: "owners",
                    localField: "ownerId",
                    foreignField: "id",
                    as: "ownerData",
                },
            },
            { $unwind: "$ownerData" },
        ];

        if (Object.keys(ownerMatchFilter).length > 1) {
            pipeline.push({ $match: ownerMatchFilter });
        }

        return pipeline;
    }

    private buildFindListDataPipeline(
        petMatchFilter: PetMatchFilter,
        ownerMatchFilter: OwnerMatchFilter,
        limit: number,
        offset: number
    ): PipelineStage[] {
        const pipeline = this.buildFindListBasePipeline(petMatchFilter, ownerMatchFilter);

        pipeline.push(
            {
                $project: {
                    _id: 0,
                    id: "$id",
                    ownerId: "$ownerId",
                    petInfo: {
                        hn: "$petInfo.hn",
                        name: "$petInfo.name",
                        species: "$petInfo.species",
                        breed: "$petInfo.breed",
                    },
                    ownerInfo: {
                        firstName: "$ownerData.ownerInfo.firstName",
                        lastName: "$ownerData.ownerInfo.lastName",
                        phoneNumber: "$ownerData.ownerInfo.phoneNumber",
                    },
                },
            },
            { $sort: { "petInfo.hn": 1 } },
            { $skip: offset },
            { $limit: limit }
        );

        return pipeline;
    }

    private buildFindListCountPipeline(petMatchFilter: PetMatchFilter, ownerMatchFilter: OwnerMatchFilter): PipelineStage[] {
        const pipeline = this.buildFindListBasePipeline(petMatchFilter, ownerMatchFilter);
        pipeline.push({ $count: "count" });
        return pipeline;
    }
}

export default new PetRepository();
