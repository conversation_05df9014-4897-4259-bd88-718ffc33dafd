import { ResponseValidationMessageEnum as v, ResponseValidationDuplicate as d } from "../../../enum/validate";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { ResponseMessageEnum as m, ResponseStatusCodeEnum as s } from "../../../enum/response";
import { DefaultError } from "../../../middlewares/errorHandler";
import { PetGenderTypeEnum, PetSpeciesEnum } from "../../../enum/pet";
import { v7 as uuidv7 } from "uuid";

const validPetUUID1 = uuidv7();
const validPetUUID2 = uuidv7();
const validOwnerUUID1 = uuidv7();
const validOwnerUUID2 = uuidv7();

const getListDefaultFilter = {
    limit: 2,
    page: 1,
};

export const getListMocks = {
    request: {
        validParams: {
            // Case 1 : [SUCCESS] Get list with default filter
            defaultFilter: {
                ...getListDefaultFilter,
            },
            // Case 2 : [SUCCESS] Get list with pet hn id filter
            petHnIdFilter: {
                ...getListDefaultFilter,
                filter: {
                    petHnId: "HN1",
                },
            },
            // Case 3 : [SUCCESS] Get list with pet name filter
            petNameFilter: {
                ...getListDefaultFilter,
                filter: {
                    petName: "Leno",
                },
            },
            // Case 4 : [SUCCESS] Get list with owner first name filter
            ownerFirstNameFilter: {
                ...getListDefaultFilter,
                filter: {
                    ownerName: "John",
                },
            },
            // Case 5 : [SUCCESS] Get list with owner last name filter
            ownerLastNameFilter: {
                ...getListDefaultFilter,
                filter: {
                    ownerName: "Doe",
                },
            },
            // Case 6 : [SUCCESS] Get list with owner full name filter
            ownerFullNameFilter: {
                ...getListDefaultFilter,
                filter: {
                    ownerName: "John Doe",
                },
            },
            // Case 7 : [SUCCESS] Get list with owner phone number filter
            ownerPhoneNumberFilter: {
                ...getListDefaultFilter,
                filter: {
                    ownerPhoneNumber: "0991234567",
                },
            },
        },
        invalidParams: {
            // Case 8 : [FAILED] Get list with invalid filter
            invalidFilter: {
                ...getListDefaultFilter,
                filter: {
                    petHnId: "",
                    petName: "",
                    ownerName: "",
                    ownerPhoneNumber: "099",
                },
            },
        },
    },
    response: {
        defaultFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID1,
                    ownerId: validOwnerUUID1,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
                {
                    petInfo: {
                        hn: "HN2",
                        name: "Luka",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID2,
                    ownerId: validOwnerUUID2,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
            ],
            count: 4,
        },
        petHnIdFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID1,
                    ownerId: validOwnerUUID1,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
            ],
            count: 1,
        },
        petNameFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID1,
                    ownerId: validOwnerUUID1,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
            ],
            count: 1,
        },
        ownerFirstNameFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID1,
                    ownerId: validOwnerUUID1,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
            ],
            count: 1,
        },
        ownerLastNameFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID1,
                    ownerId: validOwnerUUID1,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
            ],
            count: 1,
        },
        ownerFullNameFilter: {
            rows: [
                {
                    petInfo: {
                        hn: "HN1",
                        name: "Leno",
                        gender: PetGenderTypeEnum.MALE,
                        species: PetSpeciesEnum.CAT,
                        breed: "Scottish Fold",
                    },
                    id: validPetUUID1,
                    ownerId: validOwnerUUID1,
                    ownerInfo: {
                        firstName: "John",
                        lastName: "Doe",
                        phoneNumber: "0991234567",
                    },
                },
            ],
            count: 1,
        },
        ownerPhoneNumberFilter: {
            rows: [{
                petInfo: {
                    hn: "HN1",
                    name: "Leno",
                    gender: PetGenderTypeEnum.MALE,
                    species: PetSpeciesEnum.CAT,
                    breed: "Scottish Fold",
                },
                id: validPetUUID1,
                ownerId: validOwnerUUID1,
                ownerInfo: {
                    firstName: "John",
                    lastName: "Doe",
                    phoneNumber: "0991234567",
                },
            }],
            count: 1,
        },
        empty: {
            rows: [],
            count: 0,
        },
    },
    error: {
        invalidFilter: new DefaultError(e.VALIDATION_ERROR, m.INVALID_FORMAT, null, {
            petHnId: {
                _error: v.MIN_CHAR,
                _min: 1,
            },
            petName: {
                _error: v.MIN_CHAR,
                _min: 1,
            },
            ownerName: {
                _error: v.MIN_CHAR,
                _min: 1,
            },
            ownerPhoneNumber: v.PHONE,
        }),
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_LIST_PET_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
    },
};
