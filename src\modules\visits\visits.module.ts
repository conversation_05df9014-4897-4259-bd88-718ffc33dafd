import { VisitEntity, VisitAttributes } from "../../entities/visits.entity";
import { PetEntity, PetAttributes } from "../../entities/pets.entity";
import {
    ModuleError,
    RepositoryError,
    FormatValidationError,
    De<PERSON>ultError,
    <PERSON><PERSON>tyError,
    ServiceError,
} from "../../middlewares/errorHandler";
import { ErrorTypeEnum as e, ValidationErrorEnum as v } from "../../enum/errors";
import { ResponseMessageEnum as m } from "../../enum/response";
import {
    VisitRequest,
    CreateVisitFromPetRequest,
    PetInfoSubmitRequest,
    PetHealthSubmitRequest,
    PetRequirementSubmitRequest,
    PetNutritionSubmitRequest,
    PetProductMatchSubmitRequest,
    OrderSummarySubmitRequest,
    GetVisitListFilterRequest,
    GetVisitStatusCountsFilterRequest,
} from "./dto/request";
import {
    VisitResponse,
    MapNutrientsResponse,
    VisitWithOwnerListResponse,
    VisitStatusCountsResponse,
} from "./dto/response";
import { VisitRepository } from "../../repositories/visits.repository";
import { VisitStatusEnum, VisitStepEnum, MappingAlgorithmEnum } from "../../enum/visit";
import { OrderRepository } from "../../repositories/orders.repository";
import { PetRepository } from "../../repositories/pets.repository";
import { OrderAttributes, OrderEntity } from "../../entities/orders.entity";
import { PetSpeciesEnum } from "../../enum/pet";
import { NutrientTypeEnum } from "../../enum/requirement";
import { RequirementRepository } from "../../repositories/requirements.repository";
import {
    petHealthValidateConfig,
    petRequirementValidateConfig,
    petNutritionValidateConfig,
    petProductMatchValidateConfig,
} from "../../validations/visits/visits.validate.config";
import { petInfoConfig } from "../../validations/pets/pets.validate.config";
import { CreatePetWithOwnerRequest } from "../pets/dto/request";
import { PetModule } from "../pets/pets.module";

export class VisitModule {
    private readonly visitRepository;
    private readonly requirementRepository;
    private readonly orderRepository;
    private readonly petRepository;
    private readonly petModule;
    constructor() {
        this.visitRepository = new VisitRepository();
        this.requirementRepository = new RequirementRepository();
        this.orderRepository = new OrderRepository();
        this.petRepository = new PetRepository();
        this.petModule = new PetModule();
    }

    public async handlePetInfoStep(data: PetInfoSubmitRequest): Promise<boolean> {
        try {
            const visitId = data.visitId;
            const visitEntity = new VisitEntity(visitId);
            const validId = visitEntity.getId();

            const updatedVisit = await this.visitRepository.update(validId, {
                visitStep: VisitStepEnum.STEP_TWO,
                status: VisitStatusEnum.INPROGRESS,
            });

            return updatedVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.SUBMIT_PET_INFO_STEP_FAILED);
        }
    }

    public async handlePetHealthStep(data: PetHealthSubmitRequest): Promise<boolean> {
        try {
            const visitId = data.visitId;
            const petHealthData = data.petHealth;
            const partialVisitData = { petHealth: petHealthData } as VisitAttributes;

            const validPartialVisitData = new VisitEntity(visitId, partialVisitData, petHealthValidateConfig);
            const validId = validPartialVisitData.getId();
            const validData = validPartialVisitData.getAttributes().petHealth;

            const updatedVisit = await this.visitRepository.update(validId, {
                petHealth: validData,
                visitStep: VisitStepEnum.STEP_THREE,
                status: VisitStatusEnum.INPROGRESS,
            });

            return updatedVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.SUBMIT_PET_HEALTH_STEP_FAILED);
        }
    }

    public async handlePetRequirementStep(data: PetRequirementSubmitRequest): Promise<boolean> {
        try {
            const visitId = data.visitId;
            const petId = data.petId;
            const petRequirement = data.petRequirement;

            const partialVisitData = { petRequirement: petRequirement } as VisitAttributes;
            const validPartialVisitData = new VisitEntity(visitId, partialVisitData, petRequirementValidateConfig);
            const validVisitId = validPartialVisitData.getId();
            const validVisitData = validPartialVisitData.getAttributes().petRequirement;

            // Update visit's progress and visit's petRequirement
            const updatedVisit = await this.visitRepository.update(validVisitId, {
                petRequirement: validVisitData,
                visitStep: VisitStepEnum.STEP_FOUR,
                status: VisitStatusEnum.INPROGRESS,
            });

            const existingPet = await this.petRepository.findPetById(petId);
            const updatedPetInfo = {
                ...existingPet.petInfo,
                diseases: validVisitData?.diseases || [],
            };
            const partialPetData = { petInfo: updatedPetInfo } as PetAttributes;

            const petEntity = new PetEntity(petId, partialPetData, petInfoConfig);
            const validPetId = petEntity.getId();
            const validPetData = petEntity.getAttributes().petInfo;

            // Update pet's petInfo diseases
            await this.petRepository.update(validPetId, {
                petInfo: validPetData,
            });

            return updatedVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.SUBMIT_PET_REQUIREMENT_STEP_FAILED);
        }
    }

    public async handlePetNutritionStep(data: PetNutritionSubmitRequest): Promise<boolean> {
        try {
            const visitId = data.visitId;
            const petNutritionData = data.petNutrition;

            const partialVisitData = { petNutrition: petNutritionData } as VisitAttributes;

            const validPartialVisitData = new VisitEntity(visitId, partialVisitData, petNutritionValidateConfig);
            const validId = validPartialVisitData.getId();
            const validData = validPartialVisitData.getAttributes().petNutrition;

            const updatedVisit = await this.visitRepository.update(validId, {
                petNutrition: validData,
                visitStep: VisitStepEnum.STEP_FIVE,
                status: VisitStatusEnum.INPROGRESS,
            });

            return updatedVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.SUBMIT_PET_PERSONALIZE_STEP_FAILED);
        }
    }

    public async handlePetProductMatchStep(data: PetProductMatchSubmitRequest): Promise<boolean> {
        try {
            const visitId = data.visitId;
            const petProductMatchData = data.petProductMatch;

            const partialVisitData = { petProductMatch: petProductMatchData } as VisitAttributes;

            const validPartialVisitData = new VisitEntity(visitId, partialVisitData, petProductMatchValidateConfig);
            const validId = validPartialVisitData.getId();
            const validData = validPartialVisitData.getAttributes().petProductMatch;

            const updatedVisit = await this.visitRepository.update(validId, {
                petProductMatch: validData,
                visitStep: VisitStepEnum.STEP_SIX,
                status: VisitStatusEnum.INPROGRESS,
            });

            return updatedVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.SUBMIT_PET_PRODUCT_MATCH_STEP_FAILED);
        }
    }

    public async handleOrderSummaryStep(data: OrderSummarySubmitRequest): Promise<boolean> {
        try {
            const visitId = data.visitId;
            const petId = data.petId;
            const orderInfo = data.orderInfo;

            const visitEntity = new VisitEntity(visitId);
            const validvisitId = visitEntity.getId();

            const petEntity = new PetEntity(petId);
            const validPetId = petEntity.getId();

            const petData = await this.petRepository.findPetById(validPetId);
            const ownerId = petData.ownerId;

            const orderData = {
                visitId: visitId,
                petId: petId,
                ownerId: ownerId,
                orderInfo: orderInfo,
            } as OrderAttributes;
            const orderEntity = new OrderEntity(null, orderData);
            const validOrderData = orderEntity.getAttributes();

            const orderCreated = await this.orderRepository.create(validOrderData);

            const updatedVisit = await this.visitRepository.update(validvisitId, {
                visitStep: VisitStepEnum.STEP_SEVEN,
                status: VisitStatusEnum.CLOSED,
            });

            return orderCreated && updatedVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.SUBMIT_PET_ORDER_SUMMARY_STRP_FAILED);
        }
    }

    public async getPersonalizedNutrients(id: string): Promise<MapNutrientsResponse> {
        try {
            const validateId = new VisitEntity(id);
            const validId = validateId.getId();

            const visitData = await this.visitRepository.findVisitById(validId);

            const petSpecies = visitData.petInfo.species;
            const { mappingMethod, requirements } = visitData.petRequirement?.requirementsInfo ?? {};

            if (!mappingMethod || !requirements) {
                throw new ModuleError(v.FILEDS_INCOMPLETE, m.MISSING_FIELDS);
            }

            const result = await this.mapNutrients(petSpecies, requirements, mappingMethod);

            return result;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_PET_FAILED);
        }
    }

    public async getOne(id: string): Promise<VisitResponse | null> {
        try {
            const visitEntity = new VisitEntity(id);
            const validId = visitEntity.getId();
            const response = await this.visitRepository.findVisitById(validId);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ONE_VISIT_FAILED);
        }
    }

    public async getAll(): Promise<VisitResponse[]> {
        try {
            const response = await this.visitRepository.findAll();
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_ALL_VISIT_FAILED);
        }
    }

    public async getList(
        limit: number,
        page: number,
        filter?: GetVisitListFilterRequest
    ): Promise<VisitWithOwnerListResponse> {
        try {
            const visitEntity = new VisitEntity(null, null);
            if (filter) visitEntity.validateFilter(filter);

            const response = await this.visitRepository.findList(limit, page, filter);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_LIST_VISIT_FAILED);
        }
    }

    public async getStatusCounts(filter: GetVisitStatusCountsFilterRequest): Promise<VisitStatusCountsResponse> {
        try {
            const visitEntity = new VisitEntity(null, null);
            visitEntity.validateFilter(filter);

            const statusCounts = await this.visitRepository.findStatusCounts(filter);

            return statusCounts;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.GET_VISIT_STATUS_COUNTS_FAILED);
        }
    }

    public async createFromPet(data: CreateVisitFromPetRequest): Promise<void> {
        try {
            const petWithOwnerData = {
                petDetails: {
                    ownerId: data.ownerId || null,
                    petInfo: data.petInfo,
                },
                ownerDetails: data.ownerInfo
                    ? {
                          ownerInfo: data.ownerInfo,
                      }
                    : null,
            } as CreatePetWithOwnerRequest;

            // Validate owner data, create owner if not exist then validate pet data and create pet
            const createdPetWithOwner = await this.petModule.createWithOwner(petWithOwnerData);
            const createdPetId = createdPetWithOwner.pet.id;
            const createdPetInfo = createdPetWithOwner.pet.petInfo;

            const visitData = {
                petId: createdPetId,
                doctorId: data.doctorId,
                petInfo: createdPetInfo,
                doctorInfo: data.doctorInfo,
                visitStep: data.visitStep,
                status: data.status,
                petHealth: data.petHealth,
                petRequirement: data.petRequirement,
                petNutrition: data.petNutrition,
                petProductMatch: data.petProductMatch,
            } as VisitAttributes;

            const visitEntity = new VisitEntity(null, visitData);
            const validVisitData = visitEntity.getAttributes();

            await this.visitRepository.create(validVisitData);

            return;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            // Error from PetModule
            if (error instanceof DefaultError) {
                throw new DefaultError(error.errorType, error.message, null, error.error, error.statusCode);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_VISIT_FROM_PET_FAILED);
        }
    }

    public async create(data: VisitRequest): Promise<VisitResponse> {
        try {
            const visitData = data;

            const visitEntity = new VisitEntity(null, visitData);
            const validData = visitEntity.getAttributes();

            const createdVisit = await this.visitRepository.create(validData);

            const existingPet = await this.petRepository.findPetById(validData.petId);
            const updatedPetInfo = {
                ...existingPet.petInfo,
                weights: validData.petInfo.weights,
            };

            const partialPetData = { petInfo: updatedPetInfo } as PetAttributes;

            const petEntity = new PetEntity(validData.petId, partialPetData, petInfoConfig);
            const validPetId = petEntity.getId();
            const validPetData = petEntity.getAttributes().petInfo;

            await this.petRepository.update(validPetId, {
                petInfo: validPetData,
            });

            return createdVisit;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.CREATE_VISIT_FAILED);
        }
    }

    public async update(id: string, data: VisitRequest): Promise<boolean> {
        try {
            const visitData = data;

            const visitEntity = new VisitEntity(id, visitData);

            const validId = visitEntity.getId();
            const validData = visitEntity.getAttributes();

            const response = await this.visitRepository.update(validId, validData);
            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.UPDATE_VISIT_FAILED);
        }
    }

    public async delete(id: string): Promise<boolean> {
        try {
            const visitEntity = new VisitEntity(id);
            const validId = visitEntity.getId();

            const data = {
                isDeleted: true,
                deletedAt: new Date(),
            };

            const response = await this.visitRepository.update(validId, data);

            return response;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new DefaultError(e.VALIDATION_ERROR, error.message, null, error.errors);
            }
            if (error instanceof EntityError) {
                throw new DefaultError(e.ENTITY_ERROR, error.message, null, error.errors);
            }
            if (error instanceof ServiceError) {
                throw new DefaultError(e.SERVICE_ERROR, error.message, null, error.errors, error.statusCode);
            }
            if (error instanceof RepositoryError) {
                throw new DefaultError(e.REPOSITORY_ERROR, error.message, null, error.errors);
            }
            throw new DefaultError(e.INTERNAL_SERVER_ERROR, m.DELETE_VISIT_FAILED);
        }
    }

    private async mapNutrients(
        species: PetSpeciesEnum,
        requirementNames: string[],
        mappingMethod: MappingAlgorithmEnum
    ): Promise<MapNutrientsResponse> {
        try {
            const requirements = await this.requirementRepository.findRequirementsBySpecieAndNames(
                species,
                requirementNames
            );

            // Combine nutrients from all requirements
            const nutrientMap = requirements.reduce((acc, req) => {
                for (const nutrient of req.nutrients) {
                    const { nutrientName, unit, type, min, max } = nutrient;

                    if (!acc.has(nutrientName)) {
                        acc.set(nutrientName, {
                            unit,
                            type,
                            mins: [min],
                            maxs: [max],
                        });
                    } else {
                        const entry = acc.get(nutrientName)!;
                        entry.mins.push(min);
                        entry.maxs.push(max);
                    }
                }
                return acc;
            }, new Map<string, { unit: string; type: NutrientTypeEnum; mins: number[]; maxs: number[] }>());

            // Compare min max and calculate personalized nutrient values
            const mappedNutrients = Array.from(nutrientMap.entries()).map(([name, { unit, type, mins, maxs }]) => {
                if (mappingMethod === MappingAlgorithmEnum.MINMOSTFIT) {
                    return {
                        nutrientName: name,
                        unit,
                        type,
                        min: Math.max(...mins),
                        max: Math.min(...maxs),
                    };
                } else {
                    const filteredMins = mins.filter((v) => v > 0);
                    return {
                        nutrientName: name,
                        unit,
                        type,
                        min: filteredMins.length > 0 ? Math.min(...filteredMins) : 0,
                        max: Math.min(...maxs),
                    };
                }
            });

            const result = {
                nutrients: mappedNutrients,
            };

            return result;
        } catch (error) {
            throw new ModuleError(m.MAPPING_NUTRIENTS_FAILED);
        }
    }
}
