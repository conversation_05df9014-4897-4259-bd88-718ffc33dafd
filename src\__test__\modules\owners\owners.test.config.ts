import { OwnerRequest, GetAllOwnerSearchRequest, GetOwnerListFilterRequest } from "../../../modules/owners/dto/request";
import { OwnerResponse, OwnerListResponse } from "../../../modules/owners/dto/response";
import { TestCase } from "../../utils/testRunner";
import { OwnerModule } from "../../../modules/owners/owners.module";
import { getAllMocks } from "./owners.test.mock";
import { DefaultError } from "../../../middlewares/errorHandler";

type MethodTypeMapper = {
    getOne: { input: string; output: OwnerResponse | null };
    getAll: { input: { filter?: GetAllOwnerSearchRequest }; output: OwnerResponse[] };
    getList: { input: { limit: number; page: number; filter?: GetOwnerListFilterRequest }; output: OwnerListResponse };
    create: { input: { data: OwnerRequest }; output: OwnerResponse };
    update: { input: { id: string; data: OwnerRequest }; output: boolean };
    delete: { input: string; output: boolean };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof OwnerModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

const mockOwnerRepository = {
    findAll: jest.fn(),
};

jest.mock("../../../repositories/owners.repository", () => ({
    OwnerRepository: jest.fn(() => mockOwnerRepository),
}));

const moduleInstance = new OwnerModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Owners",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all owners",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.noFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.noFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.noFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of owners",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.noFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                                withArgs: [getAllMocks.request.validParams.noFilter.filter],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of owners with first name filter",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.firstNameFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.firstNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.firstNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                                withArgs: [getAllMocks.request.validParams.firstNameFilter.filter],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of owners with last name filter",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.lastNameFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.lastNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.lastNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                                withArgs: [getAllMocks.request.validParams.lastNameFilter.filter],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of owners with full name filter",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.fullNameFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.fullNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.fullNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                                withArgs: [getAllMocks.request.validParams.fullNameFilter.filter],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of owners with email filter",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.emailFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.emailFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.emailFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                                withArgs: [getAllMocks.request.validParams.emailFilter.filter],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of owners with phone number filter",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.phoneNumberFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockResolvedValue(getAllMocks.response.phoneNumberFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: getAllMocks.response.phoneNumberFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                                withArgs: [getAllMocks.request.validParams.phoneNumberFilter.filter],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw unexpected error from owner get all module",
                    setup: {
                        input: {
                            filter: getAllMocks.request.validParams.noFilter.filter,
                        },
                        mocks: () => {
                            mockOwnerRepository.findAll.mockRejectedValue(getAllMocks.error.unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getAll(input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockOwnerRepository.findAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
