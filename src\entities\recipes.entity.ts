import { RecipeIngredientEnum } from "../enum/recipe";
import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig } from "../validations/recipes/recipes.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { NutrientTypeEnum } from "../enum/requirement";

export type RecipeNutrient = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    value: number;
};

export type RecipeAttributes = {
    id: string;
    name: string;
    ingredients: RecipeIngredientEnum[];
    nutrients: RecipeNutrient[];
    isDeleted?: boolean;
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date | null;
};

export class RecipeEntity {
    private readonly id: string | null;
    private readonly petData: RecipeAttributes;
    constructor(
        id: string | null = null,
        preData: RecipeAttributes | null = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.petData = this.initialize(preData, customConfig);
    }

    private initialize(
        preData: RecipeAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): RecipeAttributes {
        try {
            this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                name: preData?.name,
                ingredients: preData?.ingredients,
                nutrients: preData?.nutrients,
            } as RecipeAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: RecipeAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateRecipeData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateRecipeData(data: RecipeAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.petData.id;
    }

    public getAttributes(): RecipeAttributes {
        return this.petData;
    }
}
