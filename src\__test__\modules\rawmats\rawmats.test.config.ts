import { TestCase } from "../../utils/testRunner";
import { RawmatModule } from "../../../modules/rawmats/rawmat.module";
import { RawmatRequest } from "../../../modules/rawmats/dto/request";
import { getAllMocks, createMocks } from "./rawmats.test.mock";
import { DefaultError, QueryError } from "../../../middlewares/errorHandler";
import { RawmatResponse } from "src/modules/rawmats/dto/response";
import { DatabaseErrorEnum as d } from "../../../enum/errors";
import { ResponseMessageEnum as m } from "../../../enum/response";

type MethodTypeMapper = {
    getAll: { input: void; output: RawmatResponse[] };
    create: { input: { data: RawmatRequest }; output: void };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof RawmatModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

export const mockService = {
    getAll: jest.fn(),
    create: jest.fn(),
    findByName: jest.fn(),
};

jest.mock("../../../repositories/rawmats.repository", () => ({
    RawmatRepository: jest.fn(() => mockService),
}));

const moduleInstance = new RawmatModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Rawmats",
    testCases: {
        getAll: {
            cases: [
                {
                    name: "[SUCCESS] should return all rawmats",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.success.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty array when no rawmats found",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: getAllMocks.response.empty.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when unexpected error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when missing key error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.missingKey.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.missingKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when invalid value error occurs",
                    setup: {
                        input: undefined,
                        mocks: () => {
                            mockService.getAll.mockResolvedValue(getAllMocks.serviceResponse.invalidValue.data);
                        },
                    },
                    method: async () => {
                        return await moduleInstance.getAll();
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getAllMocks.error.invalidValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.getAll,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
        create: {
            cases: [
                {
                    name: "[SUCCESS] should create a new rawmat",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockResolvedValue(createMocks.serviceResponse.empty.data);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: createMocks.response.success.data,
                        error: null,
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing required fields",
                    setup: {
                        input: {
                            data: createMocks.request.missingBodyKey as unknown as RawmatRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when missing fields",
                    setup: {
                        input: {
                            data: createMocks.request.emptyBody as unknown as RawmatRequest,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.missingBodyRequiredKey,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid type",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyType,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyType,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid value",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyValue,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyValue,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid min length",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyMin,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyMinLength,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw validation error when invalid max length",
                    setup: {
                        input: {
                            data: createMocks.request.invalidBodyMax,
                        },
                        mocks: () => {},
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.invalidBodyMaxLength,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: false,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw default error when unexpected error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockResolvedValue(createMocks.serviceResponse.empty.data);
                            mockService.create.mockRejectedValue(new Error("Unexpected error"));
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: true,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILURE] should throw repository error when duplicate name error occurs",
                    setup: {
                        input: {
                            data: createMocks.request.validBody,
                        },
                        mocks: () => {
                            mockService.findByName.mockImplementationOnce(
                                () => createMocks.serviceResponse.duplicateName.data
                            );
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.create(input.data);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: createMocks.error.duplicateName,
                        },
                        methodCalls: [
                            {
                                method: mockService.findByName,
                                called: true,
                            },
                            {
                                method: mockService.create,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
