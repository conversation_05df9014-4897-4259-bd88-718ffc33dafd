import { QueryError, RepositoryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { VisitAttributes } from "../entities/visits.entity";
import { withMongoDB } from "../db/mongodb";
import { GetVisitListFilterRequest, GetVisitStatusCountsFilterRequest } from "../modules/visits/dto/request";
import { Db, Document } from "mongodb";
import { DATABASE_TABLE } from "../constants/database";
import {
    VisitWithOwnerDetailResponse,
    VisitWithOwnerListResponse,
    VisitStatusCountsResponse,
} from "../modules/visits/dto/response";
import { VisitStatusEnum } from "../enum/visit";
import { escapeRegexSpecialChars } from "../utils/security";

// search object type
type PetNameSearchObject = { "petInfo.name": { $regex: string; $options: string } };
type PetHnSearchObject = { "petInfo.hn": string };
type DoctorIdSearchObject = { doctorId: string };
type DateRangeSearchObject = { createdAt: { $gte?: Date; $lte?: Date } };
type OwnerNameSearchObject =
    | { "ownerData.ownerInfo.firstName": { $regex: string; $options: string } }
    | { "ownerData.ownerInfo.lastName": { $regex: string; $options: string } }
    | { $expr: { $regexMatch: { input: { $concat: string[] }; regex: string; options: string } } };

// match filter type
type VisitFindListMatchFilter = {
    isDeleted: boolean;
    $and?: Array<PetHnSearchObject | PetNameSearchObject | DoctorIdSearchObject | DateRangeSearchObject>;
};

type OwnerFindListMatchFilter = {
    "ownerData.isDeleted": boolean;
    $or?: Array<OwnerNameSearchObject>;
};

type VisitStatusCountMatchFilter = {
    isDeleted: boolean;
    $and?: Array<DateRangeSearchObject>;
};

// default filter
const DefaultVisitFilter = {
    isDeleted: false,
};

const DefaultOwnerFilter = {
    "ownerData.isDeleted": false,
};

// pipeline stage type
type PipelineStage =
    | { $match: Document }
    | { $lookup: { from: string; localField: string; foreignField: string; as: string } }
    | { $unwind: string }
    | { $project: Document }
    | { $sort: Document }
    | { $skip: number }
    | { $limit: number }
    | { $count: string }
    | { $group: Document };

export class VisitRepository {
    async initCollection(db: Db) {
        return db.collection<VisitAttributes>(DATABASE_TABLE.VISITS);
    }

    async findVisitById(id: string): Promise<VisitAttributes> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const response = await collection.findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<VisitAttributes[]> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const filter = { isDeleted: false };
                const response = await collection
                    .find(filter, {
                        projection: {
                            _id: 0,
                        },
                    })
                    .toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(
        limit: number,
        page: number,
        filter?: GetVisitListFilterRequest
    ): Promise<VisitWithOwnerListResponse> {
        try {
            const visitMatchFilter = this.buildVisitFindListMatchFilter(filter);
            const ownerMatchFilter = this.buildOwnerFindListMatchFilter(filter);

            const offset = (page - 1) * limit;

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const dataPipeline = this.buildFindListDataPipeline(visitMatchFilter, ownerMatchFilter, limit, offset);
                const countPipeline = this.buildFindListCountPipeline(visitMatchFilter, ownerMatchFilter);

                const [rows, countResult] = await Promise.all([
                    collection.aggregate<VisitWithOwnerDetailResponse>(dataPipeline).toArray(),
                    collection.aggregate(countPipeline).toArray(),
                ]);
                const count = countResult.length > 0 ? countResult[0].count : 0;

                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findStatusCounts(filter?: GetVisitStatusCountsFilterRequest): Promise<VisitStatusCountsResponse> {
        try {
            const visitMatchFilter = this.buildStatusCountsMatchFilter(filter);

            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const dataPipeline = this.buildStatusCountsDataPipeline(visitMatchFilter);

                const results = await collection.aggregate<VisitStatusCountsResponse>(dataPipeline).toArray();

                const response = results.length > 0 ? results[0] : { waiting: 0, inProgress: 0, completed: 0 };

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: VisitAttributes): Promise<VisitAttributes> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const visitData = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdVisit = await collection.insertOne(visitData);

                if (!createdVisit) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await this.findVisitById(visitData.id);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<VisitAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db: Db) => {
                const collection = await this.initCollection(db);

                const response = await collection.updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    private buildVisitFindListMatchFilter(filter?: GetVisitListFilterRequest): VisitFindListMatchFilter {
        const visitMatchFilter: VisitFindListMatchFilter = { ...DefaultVisitFilter };
        if (filter) {
            const { petHnId, petName, doctorId, dateFrom, dateTo } = filter;
            const visitAndConditions: (
                | PetHnSearchObject
                | PetNameSearchObject
                | DoctorIdSearchObject
                | DateRangeSearchObject
            )[] = [];

            if (petHnId) visitAndConditions.push({ "petInfo.hn": petHnId });
            if (petName) visitAndConditions.push({ "petInfo.name": { $regex: `^${escapeRegexSpecialChars(petName)}`, $options: "i" } });
            if (doctorId) visitAndConditions.push({ doctorId: doctorId });
            if (dateFrom || dateTo) {
                const dateRange: DateRangeSearchObject = { createdAt: {} };
                if (dateFrom) dateRange.createdAt.$gte = new Date(dateFrom);
                if (dateTo) dateRange.createdAt.$lte = new Date(dateTo);
                visitAndConditions.push(dateRange);
            }

            if (visitAndConditions.length > 0) visitMatchFilter.$and = visitAndConditions;
        }
        return visitMatchFilter;
    }

    private buildOwnerFindListMatchFilter(filter?: GetVisitListFilterRequest): OwnerFindListMatchFilter {
        const ownerMatchFilter: OwnerFindListMatchFilter = { ...DefaultOwnerFilter };
        if (filter) {
            const { ownerName } = filter;

            if (ownerName) {
                const ownerOrConditions: OwnerNameSearchObject[] = [
                    { "ownerData.ownerInfo.firstName": { $regex: escapeRegexSpecialChars(ownerName), $options: "i" } },
                    { "ownerData.ownerInfo.lastName": { $regex: escapeRegexSpecialChars(ownerName), $options: "i" } },
                    {
                        $expr: {
                            $regexMatch: {
                                input: {
                                    $concat: ["$ownerData.ownerInfo.firstName", " ", "$ownerData.ownerInfo.lastName"],
                                },
                                regex: escapeRegexSpecialChars(ownerName),
                                options: "i",
                            },
                        },
                    },
                ];
                ownerMatchFilter.$or = ownerOrConditions;
            }
        }
        return ownerMatchFilter;
    }

    private buildStatusCountsMatchFilter(filter?: GetVisitStatusCountsFilterRequest): VisitStatusCountMatchFilter {
        const matchFilter: VisitStatusCountMatchFilter = { ...DefaultVisitFilter };

        if (filter) {
            const { dateFrom, dateTo } = filter;
            const statusCountAndconditions: DateRangeSearchObject[] = [];
            if (dateFrom || dateTo) {
                const dateRange: DateRangeSearchObject = { createdAt: {} };
                if (dateFrom) dateRange.createdAt.$gte = new Date(dateFrom);
                if (dateTo) dateRange.createdAt.$lte = new Date(dateTo);
                statusCountAndconditions.push(dateRange);
            }

            if (statusCountAndconditions.length > 0) matchFilter.$and = statusCountAndconditions;
        }

        return matchFilter;
    }

    private buildStatusCountsBasePipeline(visitMatchFilter: VisitStatusCountMatchFilter): PipelineStage[] {
        const pipeline: PipelineStage[] = [{ $match: visitMatchFilter }];
        return pipeline;
    }

    private buildStatusCountsDataPipeline(visitMatchFilter: VisitStatusCountMatchFilter): PipelineStage[] {
        const pipeline: PipelineStage[] = this.buildStatusCountsBasePipeline(visitMatchFilter);
        pipeline.push({
            $group: {
                _id: null,
                waiting: { $sum: { $cond: [{ $eq: ["$status", VisitStatusEnum.WAITING] }, 1, 0] } },
                inProgress: { $sum: { $cond: [{ $eq: ["$status", VisitStatusEnum.INPROGRESS] }, 1, 0] } },
                completed: { $sum: { $cond: [{ $eq: ["$status", VisitStatusEnum.CLOSED] }, 1, 0] } },
            },
        });
        pipeline.push({ $project: { _id: 0, waiting: 1, inProgress: 1, completed: 1 } });
        return pipeline;
    }

    private buildFindListBasePipeline(
        visitMatchFilter: VisitFindListMatchFilter,
        ownerMatchFilter: OwnerFindListMatchFilter
    ): PipelineStage[] {
        const pipeline: PipelineStage[] = [
            { $match: visitMatchFilter },
            {
                $lookup: {
                    from: "pets",
                    localField: "petId",
                    foreignField: "id",
                    as: "petData",
                },
            },
            { $unwind: "$petData" },
            {
                $lookup: {
                    from: "owners",
                    localField: "petData.ownerId",
                    foreignField: "id",
                    as: "ownerData",
                },
            },
            { $unwind: "$ownerData" },
        ];

        if (Object.keys(ownerMatchFilter).length > 0) {
            pipeline.push({ $match: ownerMatchFilter });
        }

        return pipeline;
    }

    private buildFindListDataPipeline(
        visitMatchFilter: VisitFindListMatchFilter,
        ownerMatchFilter: OwnerFindListMatchFilter,
        limit: number,
        offset: number
    ): PipelineStage[] {
        const pipeline: PipelineStage[] = this.buildFindListBasePipeline(visitMatchFilter, ownerMatchFilter);
        pipeline.push(
            {
                $project: {
                    _id: 0,
                    id: "$id",
                    petId: "$petId",
                    doctorId: "$doctorId",
                    petInfo: {
                        hn: "$petInfo.hn",
                        name: "$petInfo.name",
                        gender: "$petInfo.gender",
                        species: "$petInfo.species",
                        breed: "$petInfo.breed",
                    },
                    doctorInfo: {
                        prefix: "$doctorInfo.prefix",
                        firstName: "$doctorInfo.firstName",
                        lastName: "$doctorInfo.lastName",
                    },
                    ownerInfo: "$ownerData.ownerInfo",
                    visitStep: "$visitStep",
                    status: "$status",
                    createdAt: "$createdAt",
                },
            },
            { $sort: { createdAt: -1 } },
            { $skip: offset },
            { $limit: limit }
        );

        return pipeline;
    }

    private buildFindListCountPipeline(
        visitMatchFilter: VisitFindListMatchFilter,
        ownerMatchFilter: OwnerFindListMatchFilter
    ): PipelineStage[] {
        const pipeline: PipelineStage[] = this.buildFindListBasePipeline(visitMatchFilter, ownerMatchFilter);
        pipeline.push({ $count: "count" });
        return pipeline;
    }
}

export default new VisitRepository();
