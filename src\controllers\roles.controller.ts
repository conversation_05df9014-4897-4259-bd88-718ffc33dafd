import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { RoleModule } from "../modules/roles/roles.module";
import { ListLimitConstants } from "../constants/defaultValue";
import { requirePermission } from "../middlewares/verifyPermission";
import { PermissionEnum as p } from "../enum/permission";

export class RoleController {
    private readonly roleModule: RoleModule;
    public router: Router;
    constructor(roleModule: RoleModule) {
        this.roleModule = roleModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.use(requirePermission(p.READ_ROLE));
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));

        this.router.use(requirePermission(p.MANAGE_ROLE));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }

    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const response = await this.roleModule.getAll(token);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const token = req.body.token;
            const response = await this.roleModule.getOne(token, id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const listLimit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            const listPage = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;
            const response = await this.roleModule.getList(token, listLimit, listPage);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const role = req.body;
            const response = await this.roleModule.create(token, role);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const role = req.body;
            const response = await this.roleModule.update(token, id, role);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const token = req.body.token;
            const id = req.params.id;
            const response = await this.roleModule.delete(token, id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
