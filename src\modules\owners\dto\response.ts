import { OwnerInfo } from "../../../entities/owners.entity";

export type OwnerResponse = {
    id?: string;
    ownerInfo: OwnerInfo;
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export type OwnerListResponse = {
    rows: OwnerResponse[];
    count: number;
};

export type OwnerResolveError = {
    ownerInfo: { phoneNumber?: string } | string;
};

