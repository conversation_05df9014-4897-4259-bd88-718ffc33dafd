ARG NODE_VERSION=20.17.0
FROM node:${NODE_VERSION}-alpine AS builder
WORKDIR /app
# g++ make python3 required by node-gyp
RUN apk add --no-cache g++ make python3
RUN apk add --no-cache curl 

# Installs required node packages
COPY package*.json /app/
RUN npm install
# Builds node application
COPY . .
RUN npm run build
COPY .env ./dist/ 


# ==== Final Image
FROM node:${NODE_VERSION}-alpine AS final
WORKDIR /app
# Copying build output
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/dist/ .
# Installs required packages
RUN npm install

EXPOSE 8081
CMD ["node", "application/server.js"]
