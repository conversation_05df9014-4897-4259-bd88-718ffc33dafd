import { modulesLoader } from "./utils/testLoader";
import { runTestCase, TestCase } from "./utils/testRunner";

// Automatically discover all modules and their test files
const discoveredModules = modulesLoader();

// Run tests for all discovered modules
discoveredModules.forEach(({ moduleName, testCases }) => {
    describe(`${moduleName}Module`, () => {
        
        beforeEach(() => {
            jest.clearAllMocks();
        });

        Object.entries(testCases).forEach(([methodName, { cases }]: [string, any]) => {
            describe(methodName, () => {
                cases.forEach((testCase: TestCase<any, any>) => {
                    it(testCase.name, async () => {
                        await runTestCase(testCase)();
                    });
                });
            });
        });
    });
});