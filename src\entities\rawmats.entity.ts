import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { validated } from "../utils/validations";
import { FormatValidationError } from "../middlewares/errorHandler";
import { createConfig, receiveConfig } from "../validations/rawmats/rawmats.validate.config";
import { ResponseMessageEnum as m } from "../enum/response";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { RecipeNutrient as NutrientAttributes } from "../entities/recipes.entity";
import { RawmatRequest } from "../modules/rawmats/dto/request";
import { RawmatTypeEnum } from "../enum/rawmat";
import { RawmatResponse } from "src/modules/rawmats/dto/response";
export type RawMatAttributes = {
    rawMatId: string;
    name: string;
    nutrients: NutrientAttributes[];
    type: RawmatTypeEnum;
    costPerGram: number;
    mePerGram: number;
    createdAt: Date;
    updatedAt: Date;
    isDeleted: boolean;
    deletedAt: Date | null;
};
export class RawmatEntity {
    private readonly rawMatId: string;
    private rawMatData: RawMatAttributes;
    constructor(id: string | null, preData: RawMatAttributes | null, customConfig?: Record<string, FieldConfigType>) {
        this.rawMatId = id!;
        this.rawMatData = this.initialize(preData, customConfig);
    }

    private initialize(
        preData: RawMatAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): RawMatAttributes {
        this.validate(preData, customConfig);
        return {
            rawMatId: this.rawMatId ?? uuidv7(),
            name: preData?.name,
            nutrients: preData?.nutrients,
            type: preData?.type,
            costPerGram: preData?.costPerGram,
            mePerGram: preData?.mePerGram,
            createdAt: preData?.createdAt,
            updatedAt: preData?.updatedAt,
            isDeleted: preData?.isDeleted,
            deletedAt: preData?.deletedAt,
        } as RawMatAttributes;
    }
    private validate(preData: RawMatAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateRawMat(preData, customConfig);
        if (this.rawMatId) this.validateId(this.rawMatId);
    }
    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }
    private validateRawMat(data: RawMatAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? receiveConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public setRawmat(data: RawmatRequest, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        this.rawMatData = this.initialize(data as RawMatAttributes, schema);
    }

    public getId(): string {
        return this.rawMatId;
    }
    public getAttributes(): RawMatAttributes {
        return this.rawMatData;
    }
    public getResponse(): RawmatResponse {
        return {
            rawMatId: this.rawMatId,
            name: this.rawMatData.name,
            nutrients: this.rawMatData.nutrients,
            type: this.rawMatData.type,
            costPerGram: this.rawMatData.costPerGram,
            mePerGram: this.rawMatData.mePerGram,
            createdAt: this.rawMatData.createdAt,
            updatedAt: this.rawMatData.updatedAt,
        };
    }
}
