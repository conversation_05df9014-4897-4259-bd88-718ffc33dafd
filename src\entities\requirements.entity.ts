import { PetSpeciesEnum } from "../enum/pet";
import { ResponseMessageEnum as m } from "../enum/response";
import { EntityError, FormatValidationError } from "../middlewares/errorHandler";
import { validated } from "../utils/validations";
import { createConfig } from "../validations/requirements/requirements.validate.config";
import { idValidateConfig } from "../validations/uuid/uuid.validate.config";
import { v7 as uuidv7 } from "uuid";
import { FieldConfigType } from "../types/validate.type";
import { NutrientTypeEnum } from "../enum/requirement";

export type NutrientDetailRange = {
    nutrientName: string;
    unit: string;
    type: NutrientTypeEnum;
    min: number;
    max: number;
};

export type RequirementAttributes = {
    id: string;
    name: string;
    species: PetSpeciesEnum;
    nutrients: NutrientDetailRange[];
    isDeleted?: boolean;
    deletedAt?: Date | null;
    createdAt?: Date;
    updatedAt?: Date;
};

export class RequirementEntity {
    private readonly id: string | null;
    private readonly requirementData: RequirementAttributes;
    constructor(
        id: string | null = null,
        preData: RequirementAttributes | null = null,
        customConfig?: Record<string, FieldConfigType>
    ) {
        this.id = id;
        this.requirementData = this.initialize(preData, customConfig);
    }

    private initialize(
        preData: RequirementAttributes | null,
        customConfig?: Record<string, FieldConfigType>
    ): RequirementAttributes {
        try {
            this.validate(preData, customConfig);
            return {
                id: this.id ?? uuidv7(),
                name: preData?.name,
                species: preData?.species,
                nutrients: preData?.nutrients,
            } as RequirementAttributes;
        } catch (error) {
            if (error instanceof FormatValidationError) {
                throw new FormatValidationError(m.INVALID_FORMAT, error.errors);
            }
            throw new EntityError(m.ENTITY_INITAILIZE_ERROR, null);
        }
    }

    private validate(preData: RequirementAttributes | null, customConfig?: Record<string, FieldConfigType>): void {
        if (preData) this.validateRequirementData(preData, customConfig);
        if (this.id) this.validateId(this.id);
    }

    private validateRequirementData(data: RequirementAttributes, customConfig?: Record<string, FieldConfigType>): void {
        const schema = customConfig ?? createConfig;
        const result = validated(schema, data);
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    private validateId(id: string): void {
        const result = validated(idValidateConfig, { id });
        if (!result.isValid) {
            throw new FormatValidationError(m.INVALID_FORMAT, result.data);
        }
    }

    public getId(): string {
        return this.id ?? this.requirementData.id;
    }

    public getAttributes(): RequirementAttributes {
        return this.requirementData;
    }
}
