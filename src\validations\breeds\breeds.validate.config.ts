import { FieldConfigMap } from "../../types/validate.type";
import { BreedValidateType } from "./breeds.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { PetSpeciesEnum, DogBreedEnum, CatBreedEnum } from "../../enum/pet";

export const createConfig: FieldConfigMap<BreedValidateType> = {
    species: {
      type: v.ENUM,
      required: true,
      enumValues: Object.values(PetSpeciesEnum),
  },
    breedName: {
        type: v.ENUM,
        required: true,
        enumValues: [...Object.values(DogBreedEnum), ...Object.values(CatBreedEnum)],
    },
};
