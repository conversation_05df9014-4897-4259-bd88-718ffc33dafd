import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { DoctorModule } from "../modules/doctors/doctors.module";
import { DoctorRequest } from "../modules/doctors/dto/request";

export class Doctor<PERSON>ontroller {
    private readonly doctorModule: DoctorModule;
    public router: Router;
    constructor(doctorModule: DoctorModule) {
        this.doctorModule = doctorModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/all", this.getAll.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }
    private async getAll(_: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.doctorModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as DoctorRequest;
            const response = await this.doctorModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const body = req.body as DoctorRequest;
            const response = await this.doctorModule.update(id, body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.doctorModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
