import { TestCase } from "../../utils/testRunner";
import { PetModule } from "../../../modules/pets/pets.module";
import { PetRequest, CreatePetWithOwnerRequest, GetPetListFilterRequest } from "../../../modules/pets/dto/request";
import { PetResponse, PetWithOwnerListResponse } from "../../../modules/pets/dto/response";
import { DefaultError } from "../../../middlewares/errorHandler";
import { getListMocks } from "./pets.test.mock";

type MethodTypeMapper = {
    getOne: { input: string; output: PetResponse | null };
    getAll: { input: void; output: PetResponse[] };
    getList: {
        input: { limit: number; page: number; filter?: GetPetListFilterRequest };
        output: PetWithOwnerListResponse;
    };
    calculateBCSScore: { input: number[]; output: number };
    createWithOwner: { input: { data: CreatePetWithOwnerRequest }; output: void };
    update: { input: { id: string; data: PetRequest }; output: boolean };
    delete: { input: string; output: boolean };
};

type ModuleTestConfig = {
    name: string;
    testCases: {
        [K in keyof PetModule]?: {
            cases: TestCase<MethodTypeMapper[K]["input"], MethodTypeMapper[K]["output"]>[];
        };
    };
};

// Mock pet repository
const mockPetRepository = {
    findList: jest.fn(),
};

jest.mock("../../../repositories/pets.repository", () => ({
    PetRepository: jest.fn(() => mockPetRepository),
}));

const moduleInstance = new PetModule();

export const moduleTestConfig: ModuleTestConfig = {
    name: "Pets",
    testCases: {
        getList: {
            cases: [
                {
                    name: "[SUCCESS] should return list of pets with default filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.defaultFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page);
                    },
                    expectation: {
                        result: getListMocks.response.defaultFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of visits with default filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of pets with pet hn id filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petHnIdFilter.limit,
                            page: getListMocks.request.validParams.petHnIdFilter.page,
                            filter: getListMocks.request.validParams.petHnIdFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.petHnIdFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.petHnIdFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petHnIdFilter.limit,
                                    getListMocks.request.validParams.petHnIdFilter.page,
                                    getListMocks.request.validParams.petHnIdFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of pets with pet hn id filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petHnIdFilter.limit,
                            page: getListMocks.request.validParams.petHnIdFilter.page,
                            filter: getListMocks.request.validParams.petHnIdFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petHnIdFilter.limit,
                                    getListMocks.request.validParams.petHnIdFilter.page,
                                    getListMocks.request.validParams.petHnIdFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of pets with pet name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petNameFilter.limit,
                            page: getListMocks.request.validParams.petNameFilter.page,
                            filter: getListMocks.request.validParams.petNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.petNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.petNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petNameFilter.limit,
                                    getListMocks.request.validParams.petNameFilter.page,
                                    getListMocks.request.validParams.petNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of pets with pet name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.petNameFilter.limit,
                            page: getListMocks.request.validParams.petNameFilter.page,
                            filter: getListMocks.request.validParams.petNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.petNameFilter.limit,
                                    getListMocks.request.validParams.petNameFilter.page,
                                    getListMocks.request.validParams.petNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of pets with owner first name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFirstNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFirstNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFirstNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.ownerFirstNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerFirstNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFirstNameFilter.limit,
                                    getListMocks.request.validParams.ownerFirstNameFilter.page,
                                    getListMocks.request.validParams.ownerFirstNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of pets with owner first name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFirstNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFirstNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFirstNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFirstNameFilter.limit,
                                    getListMocks.request.validParams.ownerFirstNameFilter.page,
                                    getListMocks.request.validParams.ownerFirstNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of pets with owner last name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerLastNameFilter.limit,
                            page: getListMocks.request.validParams.ownerLastNameFilter.page,
                            filter: getListMocks.request.validParams.ownerLastNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.ownerLastNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerLastNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerLastNameFilter.limit,
                                    getListMocks.request.validParams.ownerLastNameFilter.page,
                                    getListMocks.request.validParams.ownerLastNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of pets with owner last name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerLastNameFilter.limit,
                            page: getListMocks.request.validParams.ownerLastNameFilter.page,
                            filter: getListMocks.request.validParams.ownerLastNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerLastNameFilter.limit,
                                    getListMocks.request.validParams.ownerLastNameFilter.page,
                                    getListMocks.request.validParams.ownerLastNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of pets with owner full name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFullNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFullNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFullNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.ownerFullNameFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerFullNameFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFullNameFilter.limit,
                                    getListMocks.request.validParams.ownerFullNameFilter.page,
                                    getListMocks.request.validParams.ownerFullNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of pets with owner full name filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerFullNameFilter.limit,
                            page: getListMocks.request.validParams.ownerFullNameFilter.page,
                            filter: getListMocks.request.validParams.ownerFullNameFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerFullNameFilter.limit,
                                    getListMocks.request.validParams.ownerFullNameFilter.page,
                                    getListMocks.request.validParams.ownerFullNameFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return list of pets with owner phone number filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerPhoneNumberFilter.limit,
                            page: getListMocks.request.validParams.ownerPhoneNumberFilter.page,
                            filter: getListMocks.request.validParams.ownerPhoneNumberFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.ownerPhoneNumberFilter);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.ownerPhoneNumberFilter,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerPhoneNumberFilter.limit,
                                    getListMocks.request.validParams.ownerPhoneNumberFilter.page,
                                    getListMocks.request.validParams.ownerPhoneNumberFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[SUCCESS] should return empty list of pets with owner phone number filter",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.ownerPhoneNumberFilter.limit,
                            page: getListMocks.request.validParams.ownerPhoneNumberFilter.page,
                            filter: getListMocks.request.validParams.ownerPhoneNumberFilter.filter,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockResolvedValue(getListMocks.response.empty);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: getListMocks.response.empty,
                        error: null,
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.ownerPhoneNumberFilter.limit,
                                    getListMocks.request.validParams.ownerPhoneNumberFilter.page,
                                    getListMocks.request.validParams.ownerPhoneNumberFilter.filter,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw validation error from pet module",
                    setup: {
                        input: {
                            limit: getListMocks.request.invalidParams.invalidFilter.limit,
                            page: getListMocks.request.invalidParams.invalidFilter.page,
                            filter: getListMocks.request.invalidParams.invalidFilter.filter,
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page, input.filter);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.invalidFilter,
                        },
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: false,
                            },
                        ],
                        additionalChecks: null,
                    },
                },
                {
                    name: "[FAILED] should throw unexpected error from pet module",
                    setup: {
                        input: {
                            limit: getListMocks.request.validParams.defaultFilter.limit,
                            page: getListMocks.request.validParams.defaultFilter.page,
                        },
                        mocks: () => {
                            mockPetRepository.findList.mockRejectedValue(getListMocks.error.unexpectedError);
                        },
                    },
                    method: async (input) => {
                        return await moduleInstance.getList(input.limit, input.page);
                    },
                    expectation: {
                        result: null,
                        error: {
                            instance: DefaultError,
                            properties: getListMocks.error.unexpectedError,
                        },
                        methodCalls: [
                            {
                                method: mockPetRepository.findList,
                                called: true,
                                withArgs: [
                                    getListMocks.request.validParams.defaultFilter.limit,
                                    getListMocks.request.validParams.defaultFilter.page,
                                    undefined,
                                ],
                            },
                        ],
                        additionalChecks: null,
                    },
                },
            ],
        },
    },
};
