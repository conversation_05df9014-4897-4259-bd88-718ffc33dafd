import { v4 as uuidv4 } from "uuid";
import { ErrorTypeEnum as e } from "../../../enum/errors";
import { ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../../../enum/response";
import { ResponseValidationMessageEnum as v } from "../../../enum/validate";
import { PermissionAttributes } from "../../../entities/permissions.entity";

const permissionAttributesOne = {
    id: uuidv4(),
    description: "${sample description}",
    name: "read-user",
};
const permissionAttributesTwo = {
    id: uuidv4(),
    description: "${sample description 2}",
    name: "manage-user",
};

export const getAllMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
    },
    serviceResponse: {
        success: [
            {
                ...permissionAttributesOne,
                clientRole: true,
                composite: false,
                containerId: uuidv4(),
            },
            {
                ...permissionAttributesTwo,
                clientRole: true,
                composite: false,
                containerId: uuidv4(),
            },
        ],
    },
    response: {
        success: [
            {
                ...permissionAttributesOne,
            },
            {
                ...permissionAttributesTwo,
            },
        ],
        empty: [],
        invalidKey: [
            {
                somethingElse: "value",
            },
        ],
        invalidValue: [
            {
                id: "1",
                name: "${}+*}",
            },
        ],
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_PERMISSIONS_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        validationError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                id: v.REQUIRED,
                name: v.REQUIRED,
            },
        },
    },
};

export const getOneMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            name: permissionAttributesOne.name,
        },
        notFoundParams: {
            name: "someName",
        },
        invalidParams: {
            name: "*&^)(}{$",
        },
    },
    serviceResponse: {
        success: {
            ...permissionAttributesOne,
            clientRole: true,
            composite: false,
            containerId: uuidv4(),
        },
    },
    response: {
        success: {
            ...permissionAttributesOne,
        },
        invalidKey: {
            somethingElse: "value",
        },
        invalidValue: {
            id: "1",
            name: "${}+*}",
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ONE_PERMISSION_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        validationError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
    },
};

export const getListMocks = {
    request: {
        validToken: "valid-token",
        validLimit: 10,
        validLimitOne: 1,
        validPage: 1,
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
    },
    serviceResponse: {
        success: getAllMocks.serviceResponse.success,
    },
    response: {
        success: getAllMocks.response.success,
        successWithPagination: [
            {
                ...permissionAttributesOne,
            },
        ],
        empty: [],
        invalidKey: getAllMocks.response.invalidKey,
        invalidValue: getAllMocks.response.invalidValue,
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.GET_ALL_PERMISSIONS_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
    },
};

export const createMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validBody: {
            name: "read-permission",
            description: "read-permission-description",
        },
        invalidBodyValue: {
            name: "*&^)(}{$",
        },
        missingBodyKey: {
            somethingElse: "value",
        } as any as PermissionAttributes,
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.CREATE_PERMISSION_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
    },
};

export const updateMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            name: permissionAttributesOne.name,
        },
        notFoundParams: {
            name: "someName",
        },
        invalidParams: {
            name: "*&^)(}{$",
        },
        validBody: {
            name: "read-permission",
            description: "read-permission-description",
        },
        invalidBodyValue: {
            name: "*&^)(}{$",
        },
        missingBodyKey: {
            somethingElse: "value",
        } as any as PermissionAttributes,
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.UPDATE_PERMISSION_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
    },
};

export const deleteMocks = {
    request: {
        validToken: "valid-token",
        invalidToken: "invalid-token",
        forbiddenToken: "forbidden-token",
        validParams: {
            name: permissionAttributesOne.name,
        },
        notFoundParams: {
            name: "someName",
        },
        invalidParams: {
            name: "*&^)(}{$",
        },
    },
    response: {
        success: {
            data: undefined,
        },
    },
    error: {
        serviceInitializationError: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_CONFIGURED_ERROR,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        serviceUnavailable: {
            errorType: e.SERVICE_ERROR,
            message: m.SERVICE_UNAVAILABLE,
            data: null,
            error: null,
            statusCode: s.SERVICE_UNAVAILABLE,
        },
        unexpectedError: {
            errorType: e.INTERNAL_SERVER_ERROR,
            message: m.DELETE_PERMISSION_FAILED,
            data: null,
            error: null,
            statusCode: s.INTERNAL_SERVER_ERROR,
        },
        invalidToken: {
            errorType: e.SERVICE_ERROR,
            message: m.UNAUTHENTICATED,
            data: null,
            error: null,
            statusCode: s.UNAUTHENTICATED,
        },
        forbiddenToken: {
            errorType: e.SERVICE_ERROR,
            message: m.FORBIDDEN,
            data: null,
            error: null,
            statusCode: s.FORBIDDEN,
        },
        missingKeyError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.REQUIRED,
            },
        },
        invalidValueError: {
            errorType: e.VALIDATION_ERROR,
            message: m.INVALID_FORMAT,
            data: null,
            error: {
                name: v.NO_PROHIBITED_CHARACTERS,
            },
        },
        notFound: {
            errorType: e.SERVICE_ERROR,
            message: m.NOT_FOUND,
            data: null,
            error: null,
            statusCode: s.NOT_FOUND,
        },
    },
};
