enum PetGenderTypeEnum {
    MALE = "male",
    FEMALE = "female",
}

enum PetSpeciesEnum {
    DOG = "dog",
    CAT = "cat",
}

enum ExerciseRangeEnum {
    LESSTHAN30MINS = "<30",
    BETWEEN1_5TO1HOURS = "1.5-1",
    BETWEEN1TO2HOURS = "1-2",
    BETWEEN2TO3HOURS = "2-3",
    MORETHAN3HOURS = ">3",
}

enum PetLivingEnvironmentEnum {
    INDOOR = "indoor",
    OUTDOOR = "outdoor",
    INDOOR_AND_OUTDOOR = "indoor and outdoor",
}

enum ActiveScoreCalculationMethodEnum {
    VISUAL = "visual",
    ASSESSMENT = "assessment",
}

enum ActiveScoreVisualLevelEnum {
    VERY_ACTIVE = "very active",
    MODERATELY_ACTIVE = "moderately active",
    NOT_ACTIVE = "not active",
}

enum DogBreedEnum {
    AKITA = "akita",
    ALASKAN_MALAMUTE = "alaskan malamute",
    AUSTRALIAN_SHEPHERD = "australian shepherd",
    BASENJI = "basenji",
    BASSET_HOUND = "basset hound",
    BEAGLE = "beagle",
    BELGIAN_MALINOIS = "belgian malinois",
    BICHON_FRISE = "bichon frise",
    BLOODHOUND = "bloodhound",
    BORDER_COLLIE = "border collie",
    BOSTON_TERRIER = "boston terrier",
    BOXER = "boxer",
    BRITTANY = "brittany",
    BULLDOG = "bulldog",
    CAVALIER_KING_CHARLES_SPANIEL = "cavalier king charles spaniel",
    CHIHUAHUA = "chihuahua",
    CHOW_CHOW = "chow chow",
    COCKER_SPANIEL = "cocker spaniel",
    CORGI = "corgi",
    DACHSHUND = "dachshund",
    DALMATIAN = "dalmatian",
    DOBERMAN = "doberman",
    ENGLISH_BULLDOG = "english bulldog",
    ENGLISH_SETTER = "english setter",
    ENGLISH_SPRINGER_SPANIEL = "english springer spaniel",
    FRENCH_BULLDOG = "french bulldog",
    GERMAN_SHEPHERD = "german shepherd",
    GOLDEN_RETRIEVER = "golden retriever",
    GREAT_DANE = "great dane",
    GREAT_PYRENEES = "great pyrenees",
    GREYHOUND = "greyhound",
    HAVANESE = "havanese",
    IRISH_SETTER = "irish setter",
    IRISH_WOLFHOUND = "irish wolfhound",
    JACK_RUSSELL_TERRIER = "jack russell terrier",
    KUVASZ = "kuvasz",
    LABRADOR = "labrador",
    LHASA_APSO = "lhasa apso",
    MALTESE = "maltese",
    MINIATURE_SCHNAUZER = "miniature schnauzer",
    NEWFOUNDLAND = "newfoundland",
    NORWEGIAN_ELKHOUND = "norwegian elkhound",
    OLD_ENGLISH_SHEEPDOG = "old english sheepdog",
    PEKINGESE = "pekingese",
    POMERANIAN = "pomeranian",
    POODLE = "poodle",
    PUG = "pug",
    ROTTWEILER = "rottweiler",
    SAINT_BERNARD = "saint bernard",
    SAMOYED = "samoyed",
    SCOTTISH_TERRIER = "scottish terrier",
    SHIBA_INU = "shiba inu",
    SHIH_TZU = "shih tzu",
    SIBERIAN_HUSKY = "siberian husky",
    STAFFORDSHIRE_BULL_TERRIER = "staffordshire bull terrier",
    VIZSLA = "vizsla",
    WEIMARANER = "weimaraner",
    WEST_HIGHLAND_WHITE_TERRIER = "west highland white terrier",
    WHIPPET = "whippet",
    YORKSHIRE_TERRIER = "yorkshire terrier",
}

enum CatBreedEnum {
    ABYSSINIAN = "abyssinian",
    AMERICAN_BOBTAIL = "american bobtail",
    AMERICAN_CURL = "american curl",
    AMERICAN_SHORTHAIR = "american shorthair",
    AMERICAN_WIREHAIR = "american wirehair",
    BALINESE = "balinese",
    BENGAL = "bengal",
    BIRMAN = "birman",
    BOMBAY = "bombay",
    BRITISH_LONGHAIR = "british longhair",
    BRITISH_SHORTHAIR = "british shorthair",
    BURMESE = "burmese",
    BURMILLA = "burmilla",
    CHARTREUX = "chartreux",
    COLORPOINT_SHORTHAIR = "colorpoint shorthair",
    CORNISH_REX = "cornish rex",
    CYMRIC = "cymric",
    DEVON_REX = "devon rex",
    DOMESTIC_LONGHAIR = "domestic longhair",
    DOMESTIC_SHORTHAIR = "domestic shorthair",
    EGYPTIAN_MAU = "egyptian mau",
    EXOTIC = "exotic",
    HAVANA_BROWN = "havana brown",
    HIMALAYAN = "himalayan",
    JAPANESE_BOBTAIL = "japanese bobtail",
    JAVANESE = "javanese",
    KORAT = "korat",
    LA_PERM = "la perm",
    MAINE_COON = "maine coon",
    MANX = "manx",
    NEBELUNG = "nebelung",
    NORWEGIAN_FOREST_CAT = "norwegian forest cat",
    OCICAT = "ocicat",
    ORIENTAL_LONGHAIR = "oriental longhair",
    ORIENTAL_SHORTHAIR = "oriental shorthair",
    PERSIAN = "persian",
    PETERBALD = "peterbald",
    PIXIE_BOB = "pixie bob",
    RAGDOLL = "ragdoll",
    RUSSIAN_BLUE = "russian blue",
    SAVANNAH = "savannah",
    SCOTTISH_FOLD = "scottish fold",
    SELKIRK_REX = "selkirk rex",
    SIAMESE = "siamese",
    SIBERIAN = "siberian",
    SINGAPURA = "singapura",
    SNOWSHOE = "snowshoe",
    SOMALI = "somali",
    SPHYNX = "sphynx",
    TONKINESE = "tonkinese",
    TOYGER = "toyger",
    TURKISH_ANGORA = "turkish angora",
    TURKISH_VAN = "turkish van",
}

export { PetGenderTypeEnum, PetSpeciesEnum, DogBreedEnum, CatBreedEnum, ExerciseRangeEnum, PetLivingEnvironmentEnum, ActiveScoreCalculationMethodEnum, ActiveScoreVisualLevelEnum };
