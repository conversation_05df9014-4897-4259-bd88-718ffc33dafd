import { FieldConfigMap } from "../../types/validate.type";
import { RawmatCreateValidateType, RawmatReceiveValidateType } from "./rawmats.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { NutrientConfig } from "../nutrients/nutrients.validate.config";
import { RawmatTypeEnum } from "../../enum/rawmat";

export const createConfig: FieldConfigMap<RawmatCreateValidateType> = {
    name: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 2, maxLength: 80 },
    nutrients: { type: v.OBJECT, required: true, array: true, objectConfig: NutrientConfig },
    type: { type: v.ENUM, required: true, enumValues: Object.values(RawmatTypeEnum) },
    costPerGram: { type: v.NUMBER, required: true, min: 0 },
    mePerGram: { type: v.NUMBER, required: true, min: 0 },
};

export const receiveConfig: FieldConfigMap<RawmatReceiveValidateType> = {
    rawMatId: { type: v.UUID, required: true },
    name: { type: v.NO_PROHIBITED_CHARACTERS, required: true, minLength: 2, maxLength: 80 },
    nutrients: { type: v.OBJECT, required: true, array: true, objectConfig: NutrientConfig },
    type: { type: v.ENUM, required: true, enumValues: Object.values(RawmatTypeEnum) },
    costPerGram: { type: v.NUMBER, required: true, min: 0 },
    mePerGram: { type: v.NUMBER, required: true, min: 0 },
    createdAt: { type: v.DATE, required: true },
    updatedAt: { type: v.DATE, required: true },
    isDeleted: { type: v.BOOLEAN, required: true },
    deletedAt: { type: v.DATE, required: true, allowNull: true },
};
