import { FieldConfigMap } from "../../types/validate.type";
import { RequirementValidateType, RequirementRecipeMatchingValidateType } from "./requirements.type";
import { ResponseValidationTypeEnum as v } from "../../enum/validate";
import { PetSpeciesEnum } from "../../enum/pet";

export const createConfig: FieldConfigMap<RequirementValidateType> = {
    name: { type: v.ALL_CHARS, required: true },
    species: {
        type: v.ENUM,
        required: true,
        enumValues: Object.values(PetSpeciesEnum),
    },
    nutrients: {
        type: v.NUTRIENT,
        required: true,
    },
};

export const requirementRecipeMatchingValidateConfig: FieldConfigMap<RequirementRecipeMatchingValidateType> = {
    nutrients: {
        type: v.NUTRIENT,
        required: true,
    },
};
