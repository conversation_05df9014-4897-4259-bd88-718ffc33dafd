import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { PetModule } from "../modules/pets/pets.module";
import {
    CreatePetWithOwnerRequest,
    CalculateBCSScoreRequest,
    GetPetListFilterRequest,
} from "../modules/pets/dto/request";
import { ListLimitConstants } from "../constants/defaultValue";

export class PetController {
    private readonly petModule: PetModule;
    public router: Router;
    constructor(petModule: PetModule) {
        this.petModule = petModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/bcs", this.calculateBCSScore.bind(this));
        this.router.post("/register", this.createWithOwner.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }

    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.petModule.getOne(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.petModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            const filter: GetPetListFilterRequest = {};
            if (req.query.petHnId) filter.petHnId = req.query.petHnId as string;
            if (req.query.petName) filter.petName = req.query.petName as string;
            if (req.query.ownerName) filter.ownerName = req.query.ownerName as string;
            if (req.query.ownerPhoneNumber) filter.ownerPhoneNumber = req.query.ownerPhoneNumber as string;

            const limit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            const page = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;

            const response = await this.petModule.getList(limit, page, filter);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async calculateBCSScore(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as CalculateBCSScoreRequest;
            const scores = body.scores;
            const response = await this.petModule.calculateBCSScore(scores);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async createWithOwner(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as CreatePetWithOwnerRequest;
            const response = await this.petModule.createWithOwner(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body;
            const response = await this.petModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.petModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
